import { ServiceModule } from "..";
import { ContextManager } from "../../base/context-manager";
import {
  InternalLocalMessage,
  isHumanMessage,
  LocalMessage,
  SayTool,
  FilePersistedStateType,
  InternalLocalMessage_Tool_EditFile,
  IndeterminatedWorkingSetEffectState,
  FileIndeterminateStateType,
  WebviewMessage,
  ExecuteCommandResponse,
  InternalWebviewMessage,
  DiffSet,
  DiffContent,
  EditFileResponse,
  CommandStatusCheckResponse,
  Ask,
  Say,
} from "shared/lib/agent";
import { NATIVE_BRIDGE_EVENT_NAME, WEBVIEW_BRIDGE_EVENT_NAME } from "shared/lib/bridge";
import { Bridge } from "@bridge";
import { LocalService as KwaipilotBinaryModule } from "../../core/localService";
import { ComposerSessionStorageService, isPersistedToolEditFileMessage, PersistedComposerSessionData } from "./ComposerSessionStorageService";
import { formatAskModeAsSayTool, isToolEditFileMessage } from "shared/lib/agent/isToolMessage";
import { produce } from "immer";
import { ApiConversationHistoryStorageService } from "./ApiConversationHistoryStorageService";
import { ResponseBase, ToIdeFromCoreProtocol } from "shared/lib/LocalService";
import AsyncLock from "async-lock";
import * as vscode from "vscode";
import * as path from "path";
import os from "os";
import { listFiles } from "./glob/list-files";
import { ComposerHistoryStorageService } from "./ComposerHistoryStorageService";
import { BlockCodeCacheStorageService } from "./BlockCodeCacheStorageService";
import { SerializedEditorState } from "lexical";
import { toPosixPath } from "shared/lib/agent/toPosixPath";
import { formatFilesList } from "./formatFilesList";
import { truncateToSessionName } from "./truncateToSessionName";
import { arePathsEqual } from "../../utils/path";
import { editorStateToContextualTextForLlm } from "./editorStateToContextualTextForLlm";
import { WriteToFileService } from "../write-to-file";
import { findLast, uniq } from "lodash";
import { LoggerManager } from "../../base/logger";
import { ExtensionComposerShape, WebviewContext } from "shared/lib/bridge/protocol";
import { ConfigManager, GlobalStateManager, WorkspaceStateManager } from "../../base/state-manager";
import { Config, GlobalState, WorkspaceState } from "shared/lib/state-manager/types";
import { buildDeviceLog } from "../../log/Model";
import pkg from "../../../package.json";
import curProjectInfos from "../../utils/projectInfo";
import { SupportedModelEnum, SupportedModels } from "shared/lib/agent/supportedModels";
import { WebloggerManager } from "../../base/weblogger";
import { MentionNodeV2Structure } from "shared/lib/MentionNodeV2/nodes";
import { Uri, TextDocument, ExtensionMode } from "vscode";
import { Webview } from "@webview";
import { IndexFileService } from "../index-file";
import { generateCustomUUID } from "shared/lib/misc/generateCustomUUID";
import { toRangeData } from "shared";
import { diffLines } from "diff";
import { extractTextFromFile } from "./extract-text";
import { RulesService } from "../rules";
import { SerializedDiagnostic } from "shared/lib/misc/diagnostic";
import { fromSerializedDiagnostic } from "./fromSerializedDiagnostic";
import { KwaipilotEnv } from "../../const";
import { DEFAULT_CHAT_MODE, DEFAULT_AGENT_MODE, IChatModeType, IAgentMode } from "shared/lib/business";
import { APP_NAME, DefaultBaseUrl } from "shared/lib/const";
import { NonPersistentSessionState, SessionState } from "./SessionState";
import { createLocalMessageMapper } from "./localMessageMapper";
import { UploadFile } from "shared/lib/misc/file";
import { upload } from "../../api/upload";
import { TerminalManager } from "../terminal";
import { UIPreviewService } from "../ui-preview";
import { t } from "i18next";
import { writeFileSync, existsSync, mkdirSync } from "fs";
import { LOG_PATH, TEMP_PATH } from "../../common/const";
import { Api } from "../../base/http-client";
import { ModelConfig } from "../../base/http-client/interface";
import { PerformanceTrackerManager, Tracker } from "../performance-tracker";
import { VercelOauthService } from "../vercelOauth";
import { SelectionItem } from "./SelectionList";
import { ReplaceFileService } from "../replace-file";
import { DiffLineInfo } from "shared/lib/misc/blockcode";
import { PERF_END_FLAG, PERF_START_FLAG, PerformanceLogger } from "../../base/performance-logger";
import { TokenUsageService } from "../token-usage";

const cwd = vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0) ?? ""; // may or may not exist but fs checking existence would immediately ask for permission which would be bad UX, need to come up with a better solution

const LOCK_KEY_GET_LOCAL_MESSAGES = "getLocalMessages";

export class ComposerService extends ServiceModule implements ExtensionComposerShape {
  private loggerScope = "ComposerService";
  terminalManager = this.getService(TerminalManager);

  private tracker?: Tracker;

  _sessionState = new SessionState();

  get sessionState() {
    return this._sessionState.state;
  }

  set sessionState(state: NonPersistentSessionState) {
    this._sessionState.state = state;
  }

  /**
   * 当前对话的模型
   */
  userPreferredModel: SupportedModels = SupportedModelEnum.claude3;

  modelConfigList: ModelConfig[] = [];

  /**
   * 当前对话的模式
   */
  chatMode: IChatModeType = DEFAULT_CHAT_MODE;

  /**
   * 当前对话的类型
   */
  agentMode: IAgentMode = DEFAULT_AGENT_MODE;

  lock = new AsyncLock();

  _isFirstTokenReturnMap = {} as Record<string, boolean>;

  constructor(ext: ContextManager) {
    super(ext);
    this.setupWebviewMessageListener();
    this.setupLocalServiceMessageListener();
    this.setupEditorFileActionListener();
    this.userPreferredModel = this.getBase(GlobalStateManager).get(GlobalState.COMPOSER_PREFERRED_MODEL, SupportedModelEnum.claude3);
    // 获取配置
    const defaultChatModeConfig = this.getBase(ConfigManager).get(Config.DEFAULT_CHAT_MODE);
    // 根据默认模式配置来确定新会话的聊天模式
    let newChatMode: IChatModeType = "agent"; // 默认保持现状
    if (defaultChatModeConfig === "ask") {
      // 如果设置为问答模式，则切换为问答
      newChatMode = "ask";
    }

    this.httpClient.fetchModelConfig().then((data) => {
      this.modelConfigList = data;
    });
    this.chatMode = newChatMode;
    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.addToComposerContext`, () => {
        this.logger.info("addToComposerContext", this.loggerScope);
        this.handleAddToComposerContext();
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.addFileToContext`, (uri?: vscode.Uri) => {
        this.handleAddFileToContext(uri);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.addTerminalToComposerContext`, (args: { selection: string; selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } } }) => {
        this.handleAddTerminalToComposerContext(args.selection, args.selectionRange);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.addEditorSelectionToComposerContext`, (args: { selection: string; selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }; uri: string; relativePath: string }) => {
        this.handleAddEditorSelectionToComposerContext(args.selection, args.selectionRange, args.uri, args.relativePath);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.newChat`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`newChat`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.runTerminalCommand`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`runTerminalCommand`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.skipTerminalCommand`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`skipTerminalCommand`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.switchMode`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`switchMode`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.runMcp`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`runMcp`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.skipMcp`, () => {
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$triggerShortcut(`skipMcp`);
      }),
    );

    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.updateDiffLineInfo`, (payload: { id: string; info: DiffLineInfo }) => {
        const { id, info } = payload;
        const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
        webviewBridge.$updateDiffLineInfo(id, info);
      }),
    );
  }

  $getWorkspaceFile(): string | undefined {
    return vscode.workspace.workspaceFile?.toString();
  }

  $openDiagnosticSetting(): void {
    vscode.commands.executeCommand(`${APP_NAME}.openFunctionManagement`);
  }

  async handleAddToComposerContext() {
    const textEditor = vscode.window.activeTextEditor;
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!textEditor) {
      webviewBridge.$addToComposerContext(null, "context");
    }
    else {
      const { document, selection } = textEditor;
      // 把当前 selection 扩展到一行
      const spanedRange = new vscode.Range(
        new vscode.Position(selection.start.line, 0),
        new vscode.Position(selection.end.line, document.lineAt(selection.end.line).range.end.character),
      );
      const selectionContent = document.getText(spanedRange);

      const isNotSelected = selection.start.line === selection.end.line && selection.start.character === selection.end.character;

      if (isNotSelected) {
        webviewBridge.$addToComposerContext(null, "context");
        this.logger.info("handleAddToComposerContext: Selection is not selected, skipping addition", this.loggerScope, {
          value: {
            selection,
            spanedRange,
            selectionContent,
            isNotSelected,
          },
        });
        vscode.commands.executeCommand(`${APP_NAME}.ChatWebView.toggle`);
        return;
      }

      const selectionItem: SelectionItem = {
        uri: document.uri.toString(),
        content: selectionContent,
        range: toRangeData(spanedRange),
        relativePath: vscode.workspace.asRelativePath(document.uri.fsPath),
      };

      const contextItem: MentionNodeV2Structure = {
        type: "selection",
        ...selectionItem,
      };

      // 优化重复判定逻辑：先检查前端上下文状态，再检查选择内容的相似性
      const hasContextItem = await webviewBridge.$hasContextItem(contextItem);

      if (hasContextItem) {
        // 重复或相似的selection，不添加到上下文，并触发toggle
        this.logger.info("handleAddToComposerContext: Selection is duplicate or similar, skipping addition", this.loggerScope, {
          value: selectionItem,
        });
        webviewBridge.$addToComposerContext(null, "context");
        vscode.commands.executeCommand(`${APP_NAME}.ChatWebView.toggle`);
      }
      else {
        this.logger.info("handleAddToComposerContext: Selection is not duplicate, adding to context", this.loggerScope, {
          value: selectionItem,
        });
        await this.getBase(Webview).focus("key_binding");
        webviewBridge.$addToComposerContext({
          type: "selection",
          ...selectionItem,
        }, "context");
      }
    }
  }

  /**
   * 处理将文件添加到智能体上下文的逻辑
   * 支持从文件资源管理器、编辑器标题等多个场景调用
   * @param uri 文件的 URI，如果未提供则使用当前活动编辑器的文件
   */
  async handleAddFileToContext(uri?: vscode.Uri) {
    // 首先聚焦到智能体 webview
    await this.getBase(Webview).focus("key_binding");

    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);

    // 确定要添加的文件 URI
    let targetUri: vscode.Uri | undefined = uri;

    // 如果没有提供 URI，尝试从当前活动编辑器获取
    if (!targetUri) {
      const activeEditor = vscode.window.activeTextEditor;
      if (activeEditor) {
        targetUri = activeEditor.document.uri;
      }
    }

    // 如果仍然没有找到文件，则只聚焦到智能体页面
    if (!targetUri) {
      this.logger.warn(t("handleAddFileToContext.fileNotFound"), this.loggerScope);
      webviewBridge.$addToComposerContext(null);
      return;
    }

    try {
      // 检查文件是否存在且可读
      const stat = await vscode.workspace.fs.stat(targetUri);

      if (stat.type !== vscode.FileType.File && stat.type !== vscode.FileType.Directory) {
        // 检查是否为文件（而非目录）
        this.logger.warn(t("handleAddFileToContext.notFileOrDirectory", { path: targetUri.toString() }), this.loggerScope);
        vscode.window.showWarningMessage(t("handleAddFileToContext.notFileOrDirectoryMessage", { path: vscode.workspace.asRelativePath(targetUri.fsPath) }));
        webviewBridge.$addToComposerContext(null);
        return;
      }

      // 构造文件上下文节点
      const fileContextNode: MentionNodeV2Structure = {
        type: stat.type === vscode.FileType.File ? "file" : "tree",
        uri: targetUri.toString(),
        relativePath: vscode.workspace.asRelativePath(targetUri.fsPath),
      };

      // 添加到智能体上下文
      webviewBridge.$addToComposerContext(fileContextNode, "context");

      this.logger.info("handleAddFileToContext: 成功添加文件/目录到智能体上下文", this.loggerScope, {
        value: {
          uri: targetUri.toString(),
          relativePath: fileContextNode.relativePath,
        },
      });
    }
    catch (error) {
      this.logger.error(`handleAddFileToContext: 添加文件/目录失败 ${targetUri.toString()}`, this.loggerScope, {
        err: error,
      });
      vscode.window.showErrorMessage(t("handleAddFileToContext.failedToAdd", { path: vscode.workspace.asRelativePath(targetUri.fsPath) }));
      webviewBridge.$addToComposerContext(null);
    }
  }

  /**
   * 处理终端选中内容添加到智能体上下文
   * @param selection 终端选中的文本
   * @param selectionRange 终端选区，x/y 需转换为 line/character
   */
  async handleAddTerminalToComposerContext(selection: string, selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }) {
    await this.getBase(Webview).focus("key_binding");
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!selection) {
      webviewBridge.$addToComposerContext(null);
      return;
    }

    const range = {
      start: { line: selectionRange.start.y, character: selectionRange.start.x },
      end: { line: selectionRange.end.y, character: selectionRange.end.x },
    };

    webviewBridge.$addToComposerContext({
      type: "selection",
      uri: "terminal://current", // 可自定义标识来源
      content: selection,
      range,
      relativePath: "Lines",
    }, "context");
  }

  /**
   * 处理编辑器选中内容添加到智能体上下文
   * @param selection 编辑器选中的文本
   * @param selectionRange 编辑器选区，x/y 需转换为 line/character
   */
  async handleAddEditorSelectionToComposerContext(selection: string, selectionRange: { start: { y: number; x: number }; end: { y: number; x: number } }, uri: string, relativePath: string) {
    await this.getBase(Webview).focus("key_binding");
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    if (!selection) {
      webviewBridge.$addToComposerContext(null);
      return;
    }

    const range = {
      start: { line: selectionRange.start.y, character: selectionRange.start.x },
      end: { line: selectionRange.end.y, character: selectionRange.end.x },
    };

    webviewBridge.$addToComposerContext({
      type: "selection",
      uri, // 可自定义标识来源
      content: selection,
      range,
      relativePath,
    }, "context");
  }

  async $locateByPath(maybeRelativePath: string): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<vscode.TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(t("locateByPath.failedToLocate", { path: uri.toString() }));
        this.logger.error(`locateByPathFailed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };

    const workspacePath = vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath;
    const absolutePath = maybeRelativePath.startsWith("/") ? maybeRelativePath : workspacePath ? path.join(workspacePath, maybeRelativePath) : maybeRelativePath;
    const doc = await openTextDocumentWithNotification(vscode.Uri.file(absolutePath));
    await vscode.window.showTextDocument(doc, this.viewColumn);
  }

  async $postMessageToComposerEngine(data: InternalWebviewMessage): Promise<void> {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const composerHistoryStorageService = this.getService(ComposerHistoryStorageService);
    const performanceLogger = this.getBase(PerformanceLogger);
    performanceLogger.perfStart(PERF_START_FLAG);
    if (data.type === "newTask") {
      // const localMessageUsage = await this.getService(TokenUsageService).$getSessionTokenUsage(data.reqData.sessionId);
      composerHistoryStorageService.saveUserMessage(data.questionForHumanReading, data.editorState, data.contextItems);
      this.tracker = this.getService(PerformanceTrackerManager).createTracker(data.reqData.chatId);
      this.sessionState.sessionId = data.reqData.sessionId;
      this.sessionState.chatId = data.reqData.chatId;
      this.sessionState.currentConversationDelayedUnsavedState = {
        editorState: data.editorState,
        questionForHumanReading: data.questionForHumanReading,
        contextItems: data.contextItems,
      };
      /* 用于对比这次对话新增的错误 */
      this.sessionState.diagnosticsWhenNewTaskCreated = vscode.languages.getDiagnostics();
      const deviceInfo = buildDeviceLog();
      const project = curProjectInfos?.[0];

      // 获取IndexFileService中的索引状态
      const indexFileService = this.getService(IndexFileService);
      const isIndexed = indexFileService.indexState.indexed;
      performanceLogger.perfStart('getComposerSessionData');
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      performanceLogger.perfEnd('getComposerSessionData');
      const localMessages: LocalMessage[] = session?.localMessages || [];
      performanceLogger.perfStart('getApiConversationHistory');
      const apiConversationHistory = await apiConversationHistoryStorageService.getApiConversationHistory(this.sessionState.sessionId);
      performanceLogger.perfEnd('getApiConversationHistory');
      const isChatMode = this.chatMode === "ask";

      const modelConfig = this.modelConfigList.find(v => v.type === this.userPreferredModel);
      if (!modelConfig) {
        this.logger.error("modelConfig not found", this.loggerScope, {
          value: {
            model: this.userPreferredModel,
          },
        });
      }

      // 是否启用浏览器工具
      const enableBrowserAction = this.getBase(ConfigManager).get(Config.ENABLE_BROWSER_ACTION) || false;

      const payload: WebviewMessage<"newTask"> = {
        ...data,
        phase: data.phase ?? "preResearch",
        // 过滤掉rule，不需要做为上下文
        contextItems: data.contextItems.filter(s => s.type !== "rule"),
        taskForLlm: await (async () => {
          this.logger.info("Converting editor state to contextual text for LLM", this.loggerScope, {
            value: { editorState: data.editorState, cwd, contextItemsCount: data.contextItems.length },
          });
          performanceLogger.perfStart('editorStateToContextualTextForLlm');
          const res = await editorStateToContextualTextForLlm(data.editorState, cwd, data.contextItems);
          performanceLogger.perfEnd('editorStateToContextualTextForLlm');
          return res;
        })(),
        images: data.contextItems.filter(v => v.type === "remoteImage").map(i => i.uploadInfo.url || ""),
        localMessages,
        reqData: {
          ...data.reqData,
          messages: apiConversationHistory,
          username: this.getBase(GlobalStateManager).get(GlobalState.USER_INFO)?.name,
          deviceInfo: {
            deviceId: this.getBase(GlobalStateManager).get(GlobalState.DEVICE_ID),
            deviceModel: deviceInfo.deviceModel,
            deviceName: deviceInfo.deviceName,
            deviceOsName: os.type(),
            deviceOsVersion: deviceInfo.deviceOsVersion,
            ide: KwaipilotEnv.isInIde ? `${APP_NAME}-ide` : "vscode",
            ideVersion: KwaipilotEnv.isInIde ? vscode.appVersion : vscode.version,
            platform: KwaipilotEnv.isInIde ? `${APP_NAME}-ide` : `${APP_NAME}-vscode`,
            pluginVersion: pkg.version,
          },
          projectInfo: {
            gitUrl: project?.gitRemote,
            openedFilePath: vscode.window.activeTextEditor?.document.fileName || "",
            projectName: project?.name,
          },
          model: this.userPreferredModel,
          modelConfig: modelConfig,
        },
        enableDeltaStreaming: true,
        isAskMode: isChatMode,
        agentMode: this.agentMode,
        toolSwitchState: isChatMode
          // ask 模式不写文件
          ? {
            switches: {
              edit_file: false,
              write_to_file: false,
              replace_in_file: false,
              execute_command: false,

              // ui 预览 tool 需要受控
              project_preview: this.uiPreviewEnable,

              // 浏览器工具
              browser_action: enableBrowserAction,
              test_case: enableBrowserAction,
            },
          }
          : {
            switches: {
              // ui 预览 tool 需要受控
              project_preview: this.uiPreviewEnable,

              // 浏览器工具
              browser_action: enableBrowserAction,
              test_case: enableBrowserAction,
            },
          },
      };
      /**
       * newTask 可能来源自历史消息的重新编辑\普通底部输入框的发送, 这两种情况都要重置 checkpoint 状态
       */
      performanceLogger.perfStart('setEditingMessageTs');
      await this.$setEditingMessageTs(undefined);
      performanceLogger.perfEnd('setEditingMessageTs');
      performanceLogger.perfStart('setCurrentMessageTs');
      await this.setCurrentMessageTs(undefined);
      performanceLogger.perfEnd('setCurrentMessageTs');
      // 当消息被处理后，将indexed状态关联到人类消息上
      // 使用一次性的检查来处理消息索引状态
      const currentTaskId = Date.now(); // 用于标识当前任务

      // 已有的messageList监听器会处理所有的消息列表事件
      // 我们在setupLocalServiceMessageListener中添加一个钩子来处理这次特定的任务消息
      // 保存当前任务信息供后续处理
      this.sessionState.pendingIndexedStatus = {
        taskId: currentTaskId,
        indexed: isIndexed,
      };

      this.logger.info("composer:newTask", this.loggerScope, {
        value: payload,
      });
      this.getBase(WebloggerManager).$reportUserAction({
        key: "composerNewTask",
        type: "composerNewTask",
        sessionId: this.sessionState.sessionId,
        chatId: data.reqData.chatId,
        chatMode: this.chatMode,
        agentMode: data.agentMode,
      });
      performanceLogger.perfStart('assistant/agent/local');
      kwaipilotBinaryModule.request("assistant/agent/local", payload).finally(() => {
        performanceLogger.perfEnd('assistant/agent/local');
      });
      /**
       * https://team.corp.kuaishou.com/task/B2478959
       * 如果用户从历史对话发起,应当检查当前的 indeterminatedWorkingSetEffects
       */
      this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects
        // 必须要在历史消息存在
        .filter(effect => payload.localMessages.some(m => effect.messageTs === m.ts));
    }
    else if (data.type === "stop") {
      this.tracker?.endTrace();
      // 作悲观处理
      this.sessionState.currentTaskInterrupted = true;
      await this.cleanupUnSavedDeltaMessage();
      // 取消正在应用中的文件
      this.abortComposerChat();
      this.logger.info("Posting composer state update", this.loggerScope, {
        value: { type: "stop" },
      });
      // 添加清理无效的工作集效果
      await this.cleanupInvalidWorkingSetEffects();
      await this.postComposerStateUpdate();
      await kwaipilotBinaryModule.request("assistant/agent/local", data);
      this.logger.info(`Stopped ended with currentTaskInterrupted = ${this.sessionState.currentTaskInterrupted}`, this.loggerScope);
      setTimeout(() => {
        this.logger.info(`Stopped setTimeout currentTaskInterrupted = ${this.sessionState.currentTaskInterrupted}`, this.loggerScope);
        // 避免中间异步过程导致 currentTaskInterrupted 被重置。比如异步过程中收到/messageList 消息，导致 currentTaskInterrupted 被重置为 false。
        if (!this.sessionState.currentTaskInterrupted) {
          this.sessionState.currentTaskInterrupted = true;
          this.postComposerStateUpdate();
        }
      }, 500);
    }
    else if (data.type === "restore") {
      // 继续且回退，需要等待
      // https://team.corp.kuaishou.com/task/B2489487
      // 直接转发
      this.logger.info("Requesting assistant/agent/local for restore", this.loggerScope, {
        value: { type: "restore", params: data.params },
      });
      await kwaipilotBinaryModule.request("assistant/agent/local", {
        ...data,
        params: {
          ...data.params,
        },
      });
    }
    else {
      // 直接转发
      kwaipilotBinaryModule.request("assistant/agent/local", data);
    }
    performanceLogger.perfEnd(PERF_START_FLAG);
  }

  /**
   * 上报性能日志文件到 weblogger
   * @param chatId 聊天ID
   */
  private async reportPerformanceLog(chatId: string): Promise<void> {
    try {
      const performanceLogger = this.getBase(PerformanceLogger);
      await performanceLogger.reportPerfLog(chatId, {
        maxLines: 50, // 最多上报最近50行日志
        includeRawData: false, // 不包含原始数据，只上报统计信息
      });

      this.logger.debug("Performance log reported successfully", this.loggerScope, {
        value: { chatId },
      });
    } catch (error) {
      this.logger.warn("Failed to report performance log", this.loggerScope, {
        err: error,
        value: { chatId },
      });
    }
  }


  /**
   * 清理无效的工作集效果和消息中的空路径
   * 在会话停止时调用，移除由 partial 消息导致的无效数据
   */
  async cleanupInvalidWorkingSetEffects(): Promise<void> {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);

    if (!session) {
      return;
    }

    const validIndeterminatedEffects = this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects
      .filter(effect => session.localMessages
        .some(m =>
          effect.messageTs === m.ts
          && (m.partial
            ? (m as InternalLocalMessage_Tool_EditFile)?.workingSetEffect?.path && (m as InternalLocalMessage_Tool_EditFile)?.workingSetEffect?.status !== "init"
            : true),
        ),
      );

    this.sessionState.indeterminatedWorkingSetEffects = validIndeterminatedEffects;
  }

  /**
   * 用于中断时清理未保存的 delta 消息, 重置delta数据,把 delta 信息入库
   * @returns
   */
  async cleanupUnSavedDeltaMessage(): Promise<void> {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const savedData = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);

    if (!savedData) {
      this.logger.warn("[cleanupUnSavedDeltaMessage] no saved data found", this.loggerScope);
      return;
    }
    const lastMessage = savedData.localMessages.at(-1);
    if (!lastMessage?.partial) {
      this.logger.warn(`[cleanupUnSavedDeltaMessage] last message(${lastMessage?.ts}) is not partial`, this.loggerScope);
      return;
    }
    const accumulated = this.sessionState.deltaMessageAccumulator[lastMessage.ts];
    if (!accumulated) {
      this.logger.warn(`[cleanupUnSavedDeltaMessage] no accumulated data for last message(${lastMessage.ts}) existing kesy:${Object.keys(this.sessionState.deltaMessageAccumulator)}`, this.loggerScope);
      return;
    }
    const patchedData = produce(savedData, (draft) => {
      draft.localMessages.at(-1)!.text = accumulated;
    });
    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, patchedData);
    this.sessionState.deltaMessageAccumulator = {};
  }

  async setCurrentMessageTs(commitHash: string | undefined) {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      return;
    }
    const modified = produce(session, (draft) => {
      if (commitHash) {
        const target = draft?.localMessages.find(v => v.lastCheckpointHash === commitHash);
        if (target) {
          draft.currentMessageTs = target.ts;
        }
        else {
          throw new Error("message not found");
        }
      }
      else {
        draft.currentMessageTs = undefined;
      }
    });
    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
    this.postComposerStateUpdate(modified);
  }

  async $saveContextItems(contextItems: MentionNodeV2Structure[]) {
    const { sessionId } = this.sessionState;
    if (!sessionId) {
      return;
    }

    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = (await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId))
      ||/* 获取不到的情况：发送按钮点击后，插件不会立即存储消息 而是等 messageList 返回后再存储，这时候如果调用$saveContextItems就会保存失败 */ {
      workspaceUri: this.context.storageUri?.toString() || "",
      localMessages: [],
      sessionId,
      editingMessageTs: undefined,
      currentMessageTs: undefined,
      contextItems: [],
      chatMode: this.chatMode,
      agentMode: this.agentMode,
    };

    const modified = produce(session, (draft) => {
      draft.contextItems = contextItems;
    });
    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

    this.postComposerStateUpdate(modified);
  }

  async $setEditingMessageTs(ts: number | undefined): Promise<void> {
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);

    if (!session) {
      return;
    }
    if (session.editingMessageTs === ts) {
      return;
    }
    const modified = produce(session, (draft) => {
      if (ts) {
        if (draft?.localMessages.find(v => v.ts === ts)) {
          draft.editingMessageTs = ts;
        }
        else {
          throw new Error("message not found");
        }
      }
      else {
        draft.editingMessageTs = undefined;
      }
    });

    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

    this.postComposerStateUpdate(modified);
  }

  private async truncateApiConversationHistory(erasedChatId: string) {
    const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
    const apiConversationHistory = await apiConversationHistoryStorageService.getApiConversationHistory(this.sessionState.sessionId);
    const erasedChatIndex = apiConversationHistory.findIndex(v => v.chatId === erasedChatId);
    if (erasedChatIndex === -1) {
      this.logger.warn(`truncateApiConversationHistory:conversationIndexNotFound:${erasedChatId}`, this.loggerScope);
      return apiConversationHistory;
    }
    const truncated = apiConversationHistory.slice(0, erasedChatIndex);
    await apiConversationHistoryStorageService.setApiConversationHistory(
      this.sessionState.sessionId,
      truncated,
    );
    return truncated;
  }

  async $revertHistory({ humanMessageTs, updateStateImmediately }: { humanMessageTs: number; updateStateImmediately: boolean }): Promise<void> {
    // 更新历史记录
    const session = await this.getService(ComposerSessionStorageService).getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      throw new Error("session not found");
    }
    const humanMessageI = session.localMessages.findIndex(v => v.ts === humanMessageTs);
    if (humanMessageI === -1) {
      throw new Error("humanMessageTs not found");
    }
    const humanMessage = session.localMessages[humanMessageI];

    const modified = produce(session, (draft) => {
      draft.localMessages = draft.localMessages.slice(0, humanMessageI);
    });
    if (modified.localMessages.length === 0) {
      // 删除这条记录
      await this.getService(ComposerSessionStorageService).deleteSession(modified.sessionId);
      // 历史列表中也要删除
      await this.getService(ComposerHistoryStorageService).deleteHistoryItem(modified.sessionId);
      // 清理 BlockCode 缓存
      await this.getService(BlockCodeCacheStorageService).clearSessionCache(modified.sessionId);
    }
    else {
      await this.getService(ComposerSessionStorageService).setComposerSessionData(this.sessionState.sessionId, modified);
    }
    this.sessionState.currentTaskInterrupted = true;
    this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects
      // 必须要在历史消息存在
      .filter(effect => modified.localMessages.some(m => effect.messageTs === m.ts));
    if (updateStateImmediately) {
      this.postComposerStateUpdate(modified);
    }
    if (!humanMessage.chatId) {
      throw new Error("humanMessage.chatId not found");
    }

    await this.truncateApiConversationHistory(humanMessage.chatId);
  }

  /**
   * * restore 仓库状态
   * * 更新历史消息
   * * 更新 apiConversationList
   * @param param0
   */
  async $restoreCheckpoint({
    humanMessageTs,
    restoreCommitHash,
    updateStateImmediately,
  }: {
    humanMessageTs: number;
    restoreCommitHash: string;
    updateStateImmediately: boolean;
  }) {
    await this.$postMessageToComposerEngine({
      type: "restore",
      params: {
        sessionId: this.sessionState.sessionId,
        restoreCommitHash,
      },
    });
    // 回退时处理所有 diffView
    await this.getService(WriteToFileService).rejectAll();

    await this.$revertHistory({ humanMessageTs, updateStateImmediately });
  }

  async $getDiffSet(lhsHash: string, rhsHash?: string): Promise<DiffSet[]> {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const response = await kwaipilotBinaryModule.request("assistant/agent/getDiffSet", { sessionId: this.sessionState.sessionId, lhsHash, rhsHash });
    return response.data || [];
  }

  async $setCurrentModel(model: SupportedModels): Promise<void> {
    const globalStateManager = this.getBase(GlobalStateManager);
    await globalStateManager.update(GlobalState.COMPOSER_PREFERRED_MODEL, model);
    this.userPreferredModel = model;
    await this.postComposerStateUpdate();
  }

  async $getDefaultMode(): Promise<IChatModeType> {
    const defaultChatModeConfig = this.getBase(ConfigManager).get(Config.DEFAULT_CHAT_MODE);
    return defaultChatModeConfig === "ask" ? "ask" : "agent";
  }

  /** agent 对话模式  ask | agent */
  async $setCurrentMode(chatMode: IChatModeType): Promise<void> {
    this.chatMode = chatMode;
    if (this.sessionState.sessionId) {
      // 更新当前 session 的状态
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (session) {
        session.chatMode = chatMode;
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, session);
      }
    }
    // 通知前端更新状态
    await this.postComposerStateUpdate();
  }

  /**
   * 设置当前对话的类型
   * @param agentMode "jam" | "duet"
   */
  async $setAgentMode(agentMode: IAgentMode): Promise<void> {
    this.agentMode = agentMode;
    // 如果 agentMode 是 duet，则设置 chatMode 为 agent
    if (agentMode === "duet") {
      this.chatMode = "agent";
    }
    if (this.sessionState.sessionId) {
      // 更新当前 session 的状态
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (session) {
        session.agentMode = agentMode;
        session.chatMode = "agent";
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, session);
      }
    }
    await this.postComposerStateUpdate();
  }

  /**
   * 获取当前对话的类型
   */
  async $getAgentMode(): Promise<IAgentMode> {
    return this.agentMode;
  }

  /**
   * 获取当前对话的类型 ask | agent
   * @returns IChatModeType
   */
  async $getChatMode(): Promise<IChatModeType> {
    return this.chatMode;
  }

  /**
   * 获取当前对话的类型和 agent 模式
   * @returns { chatMode: IChatModeType; agentMode: IAgentMode }
   */
  async $getCurrentChatData(): Promise<{ chatMode: IChatModeType; agentMode: IAgentMode }> {
    return {
      chatMode: this.chatMode,
      agentMode: this.agentMode,
    };
  }

  $editorStateToContextualTextForLlm(editorState: SerializedEditorState, cwd: string, contextItems: MentionNodeV2Structure[]): Promise<string> {
    return editorStateToContextualTextForLlm(editorState, cwd, contextItems);
  }

  /**
   * editor-ext
   * 编辑区操作文件
   */
  setupEditorFileActionListener() {
    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.acceptDiff`, (param: vscode.Uri) => {
        const relativePath = vscode.workspace.asRelativePath(param.fsPath);
        this.editFile.acceptFile({ filepaths: [relativePath] });
        this.updateFileStatus({
          filepaths: [relativePath],
          type: "keep",
        });
      }),
    );
    this.context.subscriptions.push(
      vscode.commands.registerCommand(`${APP_NAME}.rejectDiff`, (param: vscode.Uri) => {
        const relativePath = vscode.workspace.asRelativePath(param.fsPath);
        this.editFile.rejectFile({ filepaths: [relativePath] });
        this.updateFileStatus({
          filepaths: [relativePath],
          type: "undo",
        });
      }),
    );
  }

  /**
   * webview-ui -> ext -> kwaipilot-binary
   */
  setupWebviewMessageListener() {
    const bridge = this.getBase(Bridge);
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_FEEDBACK, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 DIFF_FEEDBACK 事件", this.loggerScope);
        return;
      }
      this.logger.info("收到 DIFF_FEEDBACK 事件", this.loggerScope, {
        value: payload,
      });
      try {
        const filepaths = payload.filepath
          ? [payload.filepath]
          : uniq(this.sessionState.indeterminatedWorkingSetEffects.filter(v => v.state === "applied").map(v => v.path));
        if (payload.type === "keep") {
          if (!payload.filepath) {
            await this.editFile.acceptAll();
          }
          else {
            await this.editFile.acceptFile({ filepaths: [payload.filepath] });
          }
        }
        else if (payload.type === "undo") {
          const partialRejectPaths = payload.partialPaths;
          if (!payload.filepath) {
            await this.editFile.rejectAll(partialRejectPaths);
          }
          else {
            await this.editFile.rejectFile({ filepaths: [payload.filepath], partialPaths: partialRejectPaths });
          }
        }

        await this.updateFileStatus({
          filepaths,
          type: payload.type,
          filesStatus: payload.filesStatus,
        });
        // 如果是离开会话的全部拒绝，需要将 appling中的文件abort并 reject掉。

        if (payload.abortChat) {
          await this.abortComposerChat();
        }
      }
      catch (e: any) {
        this.logger.error(`处理 DIFF_FEEDBACK 事件时出错 : ${e?.message || String(e)}`, this.loggerScope, {
          err: e,
        });
        throw e;
      }
    });
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_APPLY_FILE, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 APPLY_FILE 事件", this.loggerScope);
        return;
      }
      const { message } = payload;
      if (!message) {
        this.logger.warn("收到无效的 APPLY_FILE 事件", this.loggerScope);
        return;
      }
      const toolInfo = JSON.parse(message.text || "{}") as SayTool;
      if (!toolInfo.path || !toolInfo.content) {
        throw new Error("invalid toolInfo");
      }
      const absoluteFilePath = path.resolve(cwd, toolInfo.path);
      let before = "";
      try {
        before = await extractTextFromFile(absoluteFilePath);
      }
      catch (e) {
        // ignore
      }
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      const relPath = vscode.workspace.asRelativePath(toolInfo.path);
      if (this.editFile.getFileDiffState(relPath)) {
        await this.updateFileStatus({
          filepaths: [relPath],
          type: "keep",
        });
      }

      this.setIndeterminateFileStatus(message, "applying");
      this.postComposerStateUpdate(session);

      try {
        if (toolInfo.tool_version === "v2") {
          // 新版不再用 instant/apply 的方式
          await this.editFile.writeToFileV1(
            {
              path: toolInfo.path,
              before,
              after: toolInfo.content,
              diff: toolInfo.diff,
            },
          );
        }
        else {
          await this.editFile.writeToFile(
            {
              path: toolInfo.path,
              content: toolInfo.content,
              language: (toolInfo.language as any) || "default",
              instructions: toolInfo.instructions,
            },
            {
              sessionId: this.sessionState.sessionId,
              chatId: message.chatId || "",
              source: "agent-v2",
            },
          );
        }

        let diffContent: DiffContent | undefined = undefined;
        const after = await extractTextFromFile(absoluteFilePath);
        // 检查是否已经传入了新旧内容，如果是，则调用parseDiffContent
        diffContent = this.compareContents(before, after);

        this.setIndeterminateFileStatus(message, "applied");

        let modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === message.ts, "accepted");
        modified = await this.updateEditFileDiffContent(modified, m => m.ts === message.ts, diffContent);
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        // apply 不算做解除中断 this.currentTaskInterrupted = false;
        this.postComposerStateUpdate(modified);
      }
      catch (e: any) {
        this.removeIndeterminateFileStatus({ messageTs: message.ts });
        const modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === message.ts, "init");
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        // apply 不算做解除中断 this.currentTaskInterrupted = false;
        this.postComposerStateUpdate(modified);
        this.logger.error(`处理 APPLY_FILE 事件时出错 : ${e?.message || String(e)}`, this.loggerScope, {
          err: e,
        });
        throw e;
      }
    });
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_BLOCK_OPERATE, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 DIFF_BLOCK_OPERATE 事件", this.loggerScope);
        return;
      }
      const { type, filepath, diffBlock } = payload;
      if (type === "accepted") {
        this.editFile.acceptBlock({ filepath, diffBlock });
      }
      else {
        this.editFile.rejectBlock({ filepath, diffBlock });
      }
    });
    // 备用，暂时没有用到，未经过验证的方法
    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.COMPOSER_DIFF_SAVE, async (payload) => {
      const { ts, filepath, diffContent } = payload || {};
      if (!filepath || !diffContent) {
        this.logger.warn("收到无效的 DIFF_SAVE 事件", this.loggerScope);
        return;
      }
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }

      let modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === ts, "accepted");
      modified = await this.updateEditFileDiffContent(modified, m => m.ts === ts, diffContent);
      await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
      // apply 不算做解除中断 this.currentTaskInterrupted = false;
      this.postComposerStateUpdate(modified);
    });

    bridge.registerHandler(NATIVE_BRIDGE_EVENT_NAME.REVEAL_IN_EXPLORER, async (payload) => {
      if (!payload) {
        this.logger.warn("收到无效的 REVEAL_IN_EXPLORER 事件", this.loggerScope);
        return;
      }
      const { folderPath } = payload;
      if (!folderPath) {
        this.logger.warn("folderPath 参数缺失", this.loggerScope);
        return;
      }

      try {
        // 将相对路径转换为绝对路径
        const workspacePath = vscode.workspace.workspaceFolders?.at(0)?.uri.fsPath;
        const absolutePath = folderPath.startsWith("/") ? folderPath : workspacePath ? path.join(workspacePath, folderPath) : folderPath;
        const uri = vscode.Uri.file(absolutePath);

        // 使用 VSCode 命令在资源管理器中显示文件夹
        await vscode.commands.executeCommand("revealInExplorer", uri);

        this.logger.info("成功在资源管理器中显示文件夹", this.loggerScope, {
          value: { folderPath, absolutePath },
        });
      }
      catch (error) {
        this.logger.error("在资源管理器中显示文件夹失败", this.loggerScope, {
          err: error,
          value: { folderPath },
        });
        vscode.window.showErrorMessage(`无法在资源管理器中显示文件夹: ${folderPath}`);
      }
    });
  }

  /**
   * 注册接收本地包中 助理模式 引擎的消息
   */
  setupLocalServiceMessageListener() {
    const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const composerHistoryStorageService = this.getService(ComposerHistoryStorageService);
    const bridge = this.getBase(Bridge);
    kwaipilotBinaryModule.onMessage(
      "assistant/agent/writeToFile",
      async ({ data: { path: dataPath, content, diff } }) => {
        this.tracker?.recordOperation("edit_file", "api_call");
        const absoluteFilePath = path.resolve(cwd, dataPath);
        let before = "";
        try {
          before = await extractTextFromFile(absoluteFilePath);
        }
        catch (e) {
          // ignore
        }
        // 更新 workingSet
        // 更新这条消息的状态
        this.logger.info("工具调用searchAndReplace", this.loggerScope, {
          value: {
            path: dataPath,
            content,
          },
        });
        const session = await composerSessionStorageService.getComposerSessionData(
          this.sessionState.sessionId,
        );
        if (!session) {
          throw new Error("session not found");
        }
        const targetMessage = findLast(session?.localMessages, msg =>
          isToolEditFileMessage(msg),
        );
        if (!targetMessage) {
          throw new Error("targetMessage not found");
        }
        // 从当前消息中获取模式，而不是从会话级别获取
        const currentMode = targetMessage.chatMode || this.chatMode;
        // 如果是 ask 模式 不编辑文件
        if (currentMode === "ask") {
          const data: EditFileResponse = {
            type: "success",
            content,
          };
          return {
            data,
            status: "ok",
          };
        }
        const relPath = vscode.workspace.asRelativePath(dataPath);
        if (this.editFile.getFileDiffState(relPath)) {
          await this.updateFileStatus({
            filepaths: [relPath],
            type: "keep",
          });
        }
        this.setIndeterminateFileStatus(targetMessage, "applying");

        this.sessionState.currentTaskInterrupted = false;
        this.sessionState.localServiceConnectionLost = false;
        this.sessionState.localServiceRelayDied = false;
        this.postComposerStateUpdate(session);
        try {
          const details = await this.editFile.writeToFileV1(
            {
              path: relPath,
              before,
              after: content,
              diff,
            },
          );

          // 生成差异内容
          const after = content;
          const diffContent = this.compareContents(before, after);

          this.setIndeterminateFileStatus(targetMessage, "applied");

          // 获取最新会话数据并更新状态
          const latestSession
            = await composerSessionStorageService.getComposerSessionData(
              this.sessionState.sessionId,
            );
          if (!latestSession) {
            throw new Error("session not found");
          }
          let modified = await this.updateEditFileMessageFileStatus(
            latestSession,
            m => m.ts === targetMessage.ts,
            "accepted",
          );
          modified = await this.updateEditFileDiffContent(
            modified,
            m => m.ts === targetMessage.ts,
            diffContent,
          );
          await composerSessionStorageService.setComposerSessionData(
            this.sessionState.sessionId,
            modified,
          );
          this.postComposerStateUpdate(modified);

          return {
            data: { ...details },
            status: "ok",
          };
        }
        catch (e) {
          this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
          const modified = this.updateEditFileMessageFileStatus(
            session,
            m => m.ts === targetMessage.ts,
            "init",
          );
          await composerSessionStorageService.setComposerSessionData(
            this.sessionState.sessionId,
            modified,
          );

          this.postComposerStateUpdate(modified);
          throw e;
        }
        finally {
          this.tracker?.recordOperation("edit_file", "api_response");
        }
      },
    );
    kwaipilotBinaryModule.onMessage("assistant/agent/message", async ({ data: partialMessage }) => {
      this.logger.info(`receivePartialMessage`, this.loggerScope, {
        value: partialMessage,
      });
      if (partialMessage.sessionId !== this.sessionState.sessionId) {
        this.logger.warn(`sessionId不匹配 ${partialMessage.sessionId} !== ${this.sessionState.sessionId}`, this.loggerScope, {
          value: partialMessage,
        });
        return;
      }
      const savedMessage = (await composerSessionStorageService.getComposerSessionData(partialMessage.sessionId))?.localMessages.find(v => v.ts === partialMessage.ts);
      if (savedMessage && !savedMessage.partial) {
        // ide-agent 的 message 消息是 throttled, 可能会在消息完成后 messageList 之后发出 partial false 的消息
        // 因此 agent/message 的 partial=false 的消息, 应当由 messageList 处理
        return;
      }
      const toInternalLocalMessage = (localMessage: LocalMessage): InternalLocalMessage => {
        if (isHumanMessage(localMessage)) {
          if (!this.sessionState.currentConversationDelayedUnsavedState) {
            throw new Error("currentConversationDelayedUnsavedState is not set");
          }

          return {
            ...localMessage,
            role: "user" as const,
            editorState: this.sessionState.currentConversationDelayedUnsavedState.editorState,
            contextItems: this.sessionState.currentConversationDelayedUnsavedState.contextItems,
            diagnostics: [],
          };
        }
        return {
          ...localMessage,
          role: undefined,
        };
      };
      // partial message 不需要存储 sqlite
      const internalPartialMessage = toInternalLocalMessage(partialMessage);
      // 上报耗时数据
      this.processMessage({ ask: internalPartialMessage.ask, say: internalPartialMessage.say, text: internalPartialMessage.text, partial: true });
      // 处理editFile
      bridge.postOneWayMessage(
        this.getBase(Webview)._view!.webview,
        WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE, {
        partialMessage: internalPartialMessage,
      });
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/messageDelta", async ({ data: deltaMessage }) => {
      this.logger.info(`receiveDeltaMessage ${deltaMessage.text} done:${deltaMessage.done} ts:${deltaMessage.ts} type:${deltaMessage.type} keyCount:${Object.keys(this.sessionState.deltaMessageAccumulator).length}`, this.loggerScope);
      const savedMessage = (await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId))?.localMessages.find(v => v.ts === deltaMessage.ts);
      const chatId = this.sessionState.chatId;
      this._isFirstTokenReturnMap[chatId] = this._isFirstTokenReturnMap[chatId] === undefined;
      if (!savedMessage) {
        this.logger.warn(`savedMessage not found for deltaMessage ${deltaMessage.ts}`, this.loggerScope);
        return;
      }
      if (!savedMessage.partial) {
        this.logger.warn(`savedMessage is not partial ${deltaMessage.ts}`, this.loggerScope);
        return;
      }
      this.sessionState.deltaMessageAccumulator[deltaMessage.ts] = this.sessionState.deltaMessageAccumulator[deltaMessage.ts] || savedMessage.text || "";
      this.sessionState.deltaMessageAccumulator[deltaMessage.ts] += deltaMessage.text;
      const patchedMessage = produce(savedMessage, (draft) => {
        draft.text = this.sessionState.deltaMessageAccumulator[deltaMessage.ts];
      });
      bridge.postOneWayMessage(
        this.getBase(Webview)._view!.webview,
        WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_PARTIAL_MESSAGE, {
        partialMessage: patchedMessage,
      });
      if (deltaMessage.done) {
        delete this.sessionState.deltaMessageAccumulator[deltaMessage.ts];
      }
      // 打印一个首次回复的时间，和把 perf-${chatId}.log 进行上报  weblogger 
      if (this._isFirstTokenReturnMap[chatId]) {
        const performanceLogger = this.getBase(PerformanceLogger);
        performanceLogger.markLogEnded(chatId);
        // 上报 weblogger
        performanceLogger.reportPerfLog(chatId);
      }
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/messageList", async ({ data: messages }) => {
      this.logger.info(`receiveMessageList`, this.loggerScope, {
        value: messages,
      });
      await this.lock.acquire(LOCK_KEY_GET_LOCAL_MESSAGES, async () => {
        const { sessionId } = messages[0];
        if (sessionId !== this.sessionState.sessionId) {
          return;
        }

        const s = Date.now();
        const originalData: PersistedComposerSessionData = await composerSessionStorageService.getComposerSessionData(sessionId) || {
          workspaceUri: this.context.storageUri?.toString() || "",
          localMessages: [],
          sessionId,
          editingMessageTs: undefined,
          currentMessageTs: undefined,
          chatMode: this.chatMode,
          agentMode: this.agentMode,
          contextItems: [],
        };

        if (!originalData) {
          throw new Error("session data for sessionId not found");
        }

        const localMessageMapper = await createLocalMessageMapper({
          context: {
            state: this.sessionState,
            composerSessionStorageService,
            webloggerManager: this.getBase(WebloggerManager),
            configManager: this.getBase(ConfigManager),
            cwd,
            agentMode: this.agentMode,
          },
        });

        let modifiedData = produce(originalData, (draft) => {
          draft.localMessages = messages.map(localMessageMapper);
          return draft;
        });
        modifiedData = composerSessionStorageService.ensureMessagesChatMode(modifiedData, this.chatMode, this.sessionState.chatId);

        // 检查最新消息是否为 command_status_check_result
        const latestMessage = messages.at(-1);
        const isCommandStatusCheckResult = latestMessage?.type === "say" && latestMessage?.say === "command_status_check_result";

        latestMessage && this.processMessage({ ask: latestMessage.ask, say: latestMessage.say, text: latestMessage.text, partial: false });

        // 如果最新消息是 command_status_check_result，且当前任务已被中断，则不重置中断状态
        // 这样可以防止在用户点击停止按钮后，command_status_check_result 消息导致任务重新变为运行状态
        if (!isCommandStatusCheckResult) {
          // 从 engine 获取到通信时间，说明肯定不是中断的
          this.sessionState.currentTaskInterrupted = false;
        }

        // 从 engine 获取到通信时间，说明连接已恢复
        this.sessionState.localServiceConnectionLost = false;
        this.sessionState.localServiceRelayDied = false;
        // 消息有变化，需要通知 webview-ui
        this.postComposerStateUpdate(modifiedData);
        await Promise.all([
          composerHistoryStorageService.getComposerHistoryItem(sessionId)
            .then(historyItem =>
              composerHistoryStorageService.updateById(sessionId, {
                sessionId,
                name: historyItem?.name || truncateToSessionName(this.sessionState.currentConversationDelayedUnsavedState?.questionForHumanReading || "新对话"),
                workspaceUri: modifiedData.workspaceUri,
                agentMode: modifiedData.agentMode,
              }),
            ),
          composerSessionStorageService.setComposerSessionData(sessionId, modifiedData),
        ]);
        this.logger.info(`messageLoading:messageList:done:cost ${Date.now() - s}`, this.loggerScope);
      });
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/apiConversationList", async ({ data: apiConversationHistory }) => {
      const apiConversationHistoryStorageService = this.getService(ApiConversationHistoryStorageService);
      await apiConversationHistoryStorageService.setApiConversationHistory(this.sessionState.sessionId, apiConversationHistory);
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/executeCommand", async ({ data: { command, is_background, ignore_output } }) => {
      if (ignore_output) {
        this.executeCommandTool(command, is_background);
        this.tracker?.recordOperation("run_command", "complete");
        return {
          data: {
            userSkip: false,
            result: "",
            completed: true,
          },
          status: "ok",
        };
      }
      type Response = Awaited<ToIdeFromCoreProtocol["assistant/agent/executeCommand"][1]>;
      try {
        const { userSkip, result, completed } = await this.executeCommandTool(command, is_background);

        this.logger.info(`命令执行结果:${command} result:${result.slice(0, 200)}`, this.loggerScope, {});
        const res: Response = {
          data: {
            userSkip,
            result,
            completed,
          },
          status: "ok",
        };
        return res;
      }
      catch (e) {
        const errorMessage = e instanceof Error ? e.message : String(e);
        const res: Response = {
          data: {
            userSkip: false,
            result: t("executeCommand.commandFailed", { error: errorMessage }),
            completed: false,
          },
          status: "ok",
        };
        return res;
      }
      finally {
        this.tracker?.recordOperation("run_command", "complete");
      }
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/environment", async ({ data: { includeFileDetails } }) => {
      const perflog = this.getBase(PerformanceLogger);
      const s = Date.now();
      const details = await this.getEnvironmentDetails(includeFileDetails);
      perflog.logPerformance(this.sessionState.chatId, "vsc_getEnvironmentDetails", Date.now() - s);
      return {
        data: details,
        status: "ok",
      };
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/environmentV2", async () => {
      const details = await this.getEnvironmentDetailsV2();
      return {
        data: details,
        status: "ok",
      };
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/figmaToken", async () => {
      // 通过 ConfigManager 获取 FIGMA_TOKEN 配置值
      const figmaToken = this.getBase(ConfigManager).get(Config.FIGMA_TOKEN) || "";
      return {
        data: figmaToken,
        status: "ok",
      };
    });

    kwaipilotBinaryModule.onMessage("assistant/agent/editFile", async ({ data: { path: dataPath, content, language, instructions } }) => {
      const absoluteFilePath = path.resolve(cwd, dataPath);
      let before = "";
      try {
        before = await extractTextFromFile(absoluteFilePath);
      }
      catch (e) {
        // ignore
      }
      // 更新 workingSet
      // 更新这条消息的状态
      this.logger.info("工具调用editFile", this.loggerScope, {
        value: {
          path: dataPath,
          content,
          language,
          instructions,
        },
      });
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }

      const targetMessage = findLast(session?.localMessages, msg => isPersistedToolEditFileMessage(msg));
      if (!targetMessage) {
        throw new Error("targetMessage not found");
      }

      // 从当前消息中获取模式，而不是从会话级别获取
      const currentMode = targetMessage.chatMode || this.chatMode; // 如果消息没有模式信息，默认为 DEFAULT_CHAT_MODE
      // 如果是 ask 模式 不编辑文件
      if (currentMode === "ask") {
        const data: EditFileResponse = {
          type: "success",
          content,
        };
        return {
          data,
          status: "ok",
        };
      }
      const relPath = vscode.workspace.asRelativePath(dataPath);
      if (this.editFile.getFileDiffState(relPath)) {
        await this.updateFileStatus({
          filepaths: [relPath],
          type: "keep",
        });
      }
      this.setIndeterminateFileStatus(targetMessage, "applying");

      this.sessionState.currentTaskInterrupted = false;
      this.sessionState.localServiceConnectionLost = false;
      this.postComposerStateUpdate(session);
      try {
        const details = await this.editFile.writeToFile({
          path: dataPath,
          content,
          instructions,
          language,
          composer: true,
        }, {
          sessionId: this.sessionState.sessionId,
          chatId: targetMessage.chatId || "",
          source: "agent-v2",
        });
        let diffContent: DiffContent | undefined = undefined;
        if (details?.type === "success") {
          const after = await extractTextFromFile(absoluteFilePath);
          diffContent = this.compareContents(before, after);

          this.setIndeterminateFileStatus(targetMessage, "applied");
        }
        else if (details?.type === "failed") {
          this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
        }

        /* .writeToFile 可能会很长，在此期间 session 可能已经更新了，所以需要获取最新的 session */
        const latestSession = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
        if (!latestSession) {
          throw new Error("session not found");
        }
        let modified = await this.updateEditFileMessageFileStatus(latestSession, m => m.ts === targetMessage.ts, details?.type === "success" ? "accepted" : "rejected");
        modified = await this.updateEditFileDiffContent(modified, m => m.ts === targetMessage.ts, diffContent);
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        this.postComposerStateUpdate(modified);
        return {
          data: details,
          status: "ok",
        };
      }
      catch (e) {
        this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
        const modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === targetMessage.ts, "init");
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

        this.postComposerStateUpdate(modified);
        // FIXME: 和本地包通信，应该由通信层解决错误处理
        throw e;
      }
    });
    kwaipilotBinaryModule.onMessage("assistant/agent/replaceInFile", async ({ data: { path: dataPath, searchReplaceInfo, diff, sessionId, chatId } }) => {
      const absoluteFilePath = path.resolve(cwd, dataPath);
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }

      const targetMessage = findLast(session?.localMessages, msg => isPersistedToolEditFileMessage(msg));
      if (!targetMessage) {
        throw new Error("targetMessage not found");
      }
      let before = "";
      try {
        before = await extractTextFromFile(absoluteFilePath);
      }
      catch (e) {
        // ignore
      }

      // 从当前消息中获取模式，而不是从会话级别获取
      const currentMode = targetMessage.chatMode || this.chatMode; // 如果消息没有模式信息，默认为 DEFAULT_CHAT_MODE
      // 如果是 ask 模式 不编辑文件
      if (currentMode === "ask") {
        const data: EditFileResponse = {
          type: "success",
          content: "",
        };
        return {
          data,
          status: "ok",
        };
      }
      const relPath = vscode.workspace.asRelativePath(dataPath);
      if (this.editFile.getFileDiffState(relPath)) {
        await this.updateFileStatus({
          filepaths: [relPath],
          type: "keep",
        });
      }
      this.setIndeterminateFileStatus(targetMessage, "applying");

      this.sessionState.currentTaskInterrupted = false;
      this.sessionState.localServiceConnectionLost = false;
      this.postComposerStateUpdate(session);
      try {
        const details = await this.editFile.replaceFile({
          path: absoluteFilePath,
          diff,
          searchReplaceInfo,
          sessionId,
          chatId,
        });
        let diffContent: DiffContent | undefined = undefined;
        if (details?.type === "success") {
          // const diffProvider = this.editFile.diffState.get(relPath)?.diffViewProvider;
          // const diffBlocks = await diffProvider?.initAgentDiff();
          // if (!diffBlocks || diffBlocks.length === 0) {
          //   this.editFile.acceptFile({ filepaths: [relPath] });
          //   this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
          // }
          // else {
          const after = await extractTextFromFile(absoluteFilePath);
          diffContent = this.compareContents(before, after);
          this.setIndeterminateFileStatus(targetMessage, "applied");
          // }
        }
        else if (details?.type === "failed") {
          this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
        }

        /* .writeToFile 可能会很长，在此期间 session 可能已经更新了，所以需要获取最新的 session */
        const latestSession = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
        if (!latestSession) {
          throw new Error("session not found");
        }
        let modified = await this.updateEditFileMessageFileStatus(latestSession, m => m.ts === targetMessage.ts, details?.type === "success" ? "accepted" : "rejected");
        modified = await this.updateEditFileDiffContent(modified, m => m.ts === targetMessage.ts, diffContent);
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
        this.postComposerStateUpdate(modified);
        return {
          data: details,
          status: "ok",
        };
      }
      catch (e) {
        this.removeIndeterminateFileStatus({ messageTs: targetMessage.ts });
        const modified = await this.updateEditFileMessageFileStatus(session, m => m.ts === targetMessage.ts, "init");
        await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);

        this.postComposerStateUpdate(modified);
        // FIXME: 和本地包通信，应该由通信层解决错误处理
        throw e;
      }
    });
    kwaipilotBinaryModule.onMessage("uiPreview/info", ({ data }) => {
      this.handleUiPreviewInfo(data);
    });
    // vercel 授权
    kwaipilotBinaryModule.onMessage("deploy/token", ({ data }) => {
      console.log("jmm deploy/token", data);
      const vercelOauthService = this.getService(VercelOauthService);
      vercelOauthService.handleTocken(data);
    });
    // TEST: 测试专用
    if (this.context.extensionMode === ExtensionMode.Development) {
      vscode.commands.registerCommand(`${APP_NAME}.test.uiPreview`, async (payload) => {
        this.handleUiPreviewInfo(payload);
      });
    }
    kwaipilotBinaryModule.onMessage("assistant/agent/commandStatusCheck", async ({ data: { check_duration } }) => {
      try {
        const data = await this.terminalManager.getCommandCheckResultWithDelay(check_duration);
        return {
          data,
          status: "ok",
        } as ResponseBase<CommandStatusCheckResponse>;
      }
      catch (error: any) {
        return {
          data: {
            status: "error",
            output: "获取最新输出失败",
          },
          status: "ok",
        } as ResponseBase<CommandStatusCheckResponse>;
      }
      finally {
        // 如果执行结束了
        this.$postMessageToComposerEngine({
          type: "askResponse",
          askResponse: "yesButtonClicked",
          text: "",
        });
      }
    });
    kwaipilotBinaryModule.on("restart", async () => {
      // 如果当前正在流式输出中, 会导致丢失状态
      if (!this.sessionState.sessionId) {
        return;
      }
      this.sessionState.localServiceConnectionLost = true;
      this.sessionState.currentTaskInterrupted = true;
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      this.postComposerStateUpdate(session);
    });
    kwaipilotBinaryModule.on("restart-failed", async () => {
      // 如果当前正在流式输出中, 会导致丢失状态
      if (!this.sessionState.sessionId) {
        return;
      }
      this.sessionState.localServiceRelayDied = true;
      this.sessionState.currentTaskInterrupted = true;
      const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
      if (!session) {
        throw new Error("session not found");
      }
      this.postComposerStateUpdate(session);
    });
  }

  private processMessage(message: { ask?: Ask; say?: Say; text?: string; partial?: boolean }) {
    if (message.ask === "tool" || message.say === "tool") {
      const tool = JSON.parse(message?.text || "{}") as SayTool;
      const { path, searchReplaceInfo } = tool;
      if (tool) {
        switch (tool.tool) {
          case "grepSearch":
            return;
          case "codebaseSearch":
            return;
          case "listFilesTopLevel":
          case "listFilesRecursive":
            return;
          case "readFile":
            return;
          case "editFile":
            this.tracker?.recordOperation("edit_file", "start");
            if (!path || !searchReplaceInfo || searchReplaceInfo.length === 0) {
              return;
            }
            this.replaceService.processMessage({
              path,
              searchReplaceInfo,
              partial: message.partial,
            });
            break;
          case "parseFigma":
            return;
        }
      }
    }
    else if (message.say === "command") {
      this.tracker?.recordOperation("run_command", "start");
    }
    else if (message.say === "completion_result") {
      this.tracker?.endTrace();
    }
  }

  async handleUiPreviewInfo(data: ToIdeFromCoreProtocol["uiPreview/info"][0]) {
    console.log("jmm handleUiPreviewInfo", data);
    const webviewBridge = this.getBase(Webview).rpcContext.getProxy(WebviewContext.WebviewComposer);
    const toStructure = async (): Promise<MentionNodeV2Structure | null> => {
      if (data.type === "error") {
        return {
          type: "error",
          label: data.displayName,
          message: data.data,
          desc: Array.isArray(data.desc) ? (data.desc || []) : [data.desc || ""],
          uri: `error://${Date.now()}`,
          relativePath: "",
        };
      }
      if (data.type === "element") {
        return {
          type: "dom",
          uri: `dom://${Date.now()}`,
          xml: data.data,
          label: data.displayName,
          relativePath: "",
          focusInfo: data.focusInfo,
        };
      }
      if (data.type === "image") {
        const uri = Uri.file(data.data);
        const uploadInfo = await new Promise<UploadFile>((resolve) => {
          upload(this.getBase(ConfigManager).get(Config.PROXY_URL) || DefaultBaseUrl, {
            file: uri,
            onStart: () => {
            },
            onProgress: () => {
            },
            onSuccess: (data) => {
              resolve(data);
            },
            onFailed: (file) => {
              console.error(file);
            },
          });
        });
        return {
          type: "remoteImage",
          uri: uri.toString(),
          relativePath: vscode.workspace.asRelativePath(uri),
          uploadInfo,
        };
      }
      const neverType: never = data.type;
      console.warn("unexpected type", neverType);
      return null;
    };
    const structure = await toStructure();
    if (!structure) {
      return;
    }
    if (structure.type === "remoteImage") {
      webviewBridge.$addToComposerContext(structure, "context");
    }
    else {
      webviewBridge.$addToComposerContext(structure, "cursor");
    }
    return;
  }

  /**
   * 终止对话的副作用
   * @param message
   * @param status
   */
  async abortComposerChat() {
    const applyingFiles = uniq(this.sessionState.indeterminatedWorkingSetEffects.filter(v => v.state === "applying").map(v => v.path));
    for (const relPath of applyingFiles) {
      await this.editFile.abortApply(relPath);
    }
    await this.updateFileStatus({
      filepaths: applyingFiles,
      type: "undo",
    });
  }

  private async setIndeterminateFileStatus(message: InternalLocalMessage, status: FileIndeterminateStateType) {
    const toolInfo = JSON.parse(message.text || "{}") as SayTool;
    const samePathEffectI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(v => v.path === toolInfo.path);
    if (samePathEffectI !== -1 && this.sessionState.indeterminatedWorkingSetEffects[samePathEffectI].state === "applied") {
      //  之前的消息没有接受 or 拒绝，则清空，默认接受
      this.sessionState.indeterminatedWorkingSetEffects.splice(samePathEffectI, 1);
    }
    const messageTs = message.ts;
    const targetI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(i => i.messageTs === messageTs);
    if (targetI === -1) {
      this.sessionState.indeterminatedWorkingSetEffects.push({ messageTs, state: status, path: toolInfo.path || "" });
    }
    else {
      this.sessionState.indeterminatedWorkingSetEffects[targetI].state = status;
    }
  }

  private async removeIndeterminateFileStatus(selector: { messageTs?: number; path?: string }) {
    const predicate: (v: IndeterminatedWorkingSetEffectState) => boolean = selector.messageTs
      ? v => v.messageTs === selector.messageTs
      : selector.path
        ? v => v.path === selector.path
        : () => false;
    const targetI = this.sessionState.indeterminatedWorkingSetEffects.findIndex(predicate);
    if (targetI === -1) {
      return;
    }
    this.sessionState.indeterminatedWorkingSetEffects.splice(targetI, 1);
  }

  /**
   * 按文件路径分别更新消息文件状态
   * @param session 会话数据
   * @param filesStatus 文件状态映射，键为文件路径，值为状态
   * @returns 修改后的会话数据
   */
  private updateEditFileMessageFileStatusByPath(
    session: PersistedComposerSessionData,
    filesStatus: { [filepath: string]: "accepted" | "rejected" },
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(isToolEditFileMessage) as InternalLocalMessage_Tool_EditFile[];

      targets.forEach((target) => {
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        const filePath = toolInfo.path || "";

        // 检查该文件是否在 filesStatus 中有对应的状态
        if (filesStatus[filePath]) {
          target.workingSetEffect = target.workingSetEffect || {
            status: "init",
            language: toolInfo.language || "",
            path: filePath,
            diffContent: undefined,
          };
          target.workingSetEffect.status = filesStatus[filePath];
        }
      });
    });

    return modified;
  }

  /**
   * 更新编辑文件消息的文件状态
   * @param session 会话数据
   * @param selector 消息选择器函数
   * @param persistedStatus 持久化状态
   * @returns 修改后的会话数据
   */
  private updateEditFileMessageFileStatus(
    session: PersistedComposerSessionData,
    selector: (message: InternalLocalMessage_Tool_EditFile) => boolean,
    persistedStatus: FilePersistedStateType,
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(msg => isToolEditFileMessage(msg) || msg.chatMode === "ask")
        .filter(m => selector(m as InternalLocalMessage_Tool_EditFile)) as InternalLocalMessage_Tool_EditFile[];
      targets.forEach((target) => {
        if (target.chatMode === "ask") {
          const tool = formatAskModeAsSayTool(target);
          if (tool) {
            target.workingSetEffect = target.workingSetEffect || {
              status: persistedStatus,
              language: tool.language,
              path: tool.path,
              diffContent: tool.content,
            };
          }
          target.workingSetEffect.status = persistedStatus;
          return;
        }
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        target.workingSetEffect = target.workingSetEffect || {
          status: "init",
          language: toolInfo.language || "",
          path: toolInfo.path || "",
          diffContent: undefined,
        };
        target.workingSetEffect.status = persistedStatus;
      });
    });

    return modified;
  }

  private async updateEditFileDiffContent(
    session: PersistedComposerSessionData,
    selector: (message: InternalLocalMessage_Tool_EditFile) => boolean,
    diffContent?: DiffContent | null,
  ) {
    /**
     * 更新 sqlite
     */
    const modified: PersistedComposerSessionData = produce(session, (draft) => {
      const targets = draft.localMessages
        .filter(isToolEditFileMessage)
        .filter(m => selector(m as InternalLocalMessage_Tool_EditFile)) as InternalLocalMessage_Tool_EditFile[];
      targets.forEach((target) => {
        const toolInfo = JSON.parse(target.text || "{}") as SayTool;
        target.workingSetEffect = target.workingSetEffect || {
          status: "init",
          language: toolInfo.language || "",
          path: toolInfo.path || "",
          diffContent: undefined,
        };
        target.workingSetEffect.diffContent = diffContent || undefined;
      });
    });

    return modified;
  }

  async updateFileStatus(payload: {
    filepaths: string[];
    type: "keep" | "undo";
    filesStatus?: { [filepath: string]: "accepted" | "rejected" };
  }) {
    // 更新状态
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    const session = await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId);
    if (!session) {
      throw new Error("session not found");
    }
    this.sessionState.indeterminatedWorkingSetEffects = this.sessionState.indeterminatedWorkingSetEffects.filter(v => !payload.filepaths.includes(v.path));

    let modified: PersistedComposerSessionData;

    // 如果提供了 filesStatus，则使用按文件路径分别设置状态的方式
    if (payload.filesStatus && Object.keys(payload.filesStatus).length > 0) {
      modified = this.updateEditFileMessageFileStatusByPath(session, payload.filesStatus);
    }
    else {
      // 兼容旧的方式：使用统一的 type 来设置所有文件的状态
      modified = this.updateEditFileMessageFileStatus(session, /* 更新所有消息 */(m) => {
        if (m.chatMode === "ask") {
          const tool = formatAskModeAsSayTool(m);
          return payload.filepaths.includes(tool?.path || "");
        }
        else {
          const toolInfo = JSON.parse(m.text || "{}") as SayTool;
          return payload.filepaths.includes(toolInfo.path || "");
        }
      }, payload.type === "keep" ? "accepted" : "rejected");
    }

    await composerSessionStorageService.setComposerSessionData(this.sessionState.sessionId, modified);
    this.postComposerStateUpdate(modified);
  }

  protected postComposerStateUpdate(): Promise<void>;
  protected postComposerStateUpdate(modified: PersistedComposerSessionData): void;
  protected async postComposerStateUpdate(modified?: PersistedComposerSessionData) {
    this.logger.info(`postComposerStateUpdate modified sessionId ${modified?.sessionId || ""}`, this.loggerScope);
    if (!modified) {
      const composerSessionStorageService = this.getService(ComposerSessionStorageService);
      const session: PersistedComposerSessionData | undefined = this.sessionState.sessionId
        ? await composerSessionStorageService.getComposerSessionData(this.sessionState.sessionId)
        : {
          localMessages: [],
          workspaceUri: "",
          sessionId: "",
          editingMessageTs: undefined,
          currentMessageTs: undefined,
          chatMode: this.chatMode,
          agentMode: this.agentMode,
          contextItems: [],
        };

      if (!session) {
        // 新建聊天时 session 可能还未初始化，返回一个空的 session 数据结构
        modified = {
          localMessages: [],
          workspaceUri: "",
          sessionId: this.sessionState.sessionId || "",
          editingMessageTs: undefined,
          currentMessageTs: undefined,
          chatMode: this.chatMode,
          agentMode: this.agentMode,
          userPreferredModel: this.userPreferredModel,
        } as unknown as PersistedComposerSessionData;
      }
      else {
        modified = session;
      }
      this.logger.info(`postComposerStateUpdate sessionId ${this.sessionState.sessionId}, sessionsessionId ${session?.sessionId || ""}, final modified sessionId ${modified?.sessionId || ""}`, this.loggerScope);
    }
    const bridge = this.getBase(Bridge);
    bridge.postOneWayMessage(
      this.getBase(Webview)._view!.webview,
      WEBVIEW_BRIDGE_EVENT_NAME.COMPOSER_STATE_UPDATE, {
      localMessages: modified.localMessages,
      workspaceUri: modified.workspaceUri,
      sessionId: modified.sessionId,
      currentTaskInterrupted: this.sessionState.currentTaskInterrupted,
      indeterminatedWorkingSetEffects: this.sessionState.indeterminatedWorkingSetEffects,
      // 都为空也认为是 currentWorkspace
      isCurrentWorkspaceSession: (this.context.storageUri?.toString() || "") === modified.workspaceUri,
      editingMessageTs: modified.editingMessageTs,
      currentMessageTs: modified.currentMessageTs,
      userPreferredModel: this.userPreferredModel,
      localServiceConnectionLost: this.sessionState.localServiceConnectionLost,
      localServiceRelayDied: this.sessionState.localServiceRelayDied,
      indexed: modified.indexed,
      contextItems: modified.contextItems,
      // 传递缓存路径信息给前端
      cachePathInfos: modified.cachePathInfos || [],
      chatMode: modified.chatMode || this.chatMode,
      agentMode: modified.agentMode || this.agentMode,
    });
    if (modified.chatMode && modified.chatMode !== this.chatMode) {
      this.chatMode = modified.chatMode; // 更新当前会话的 chatMode
    }
    if (modified.agentMode && modified.agentMode !== this.agentMode) {
      this.agentMode = modified.agentMode; // 更新当前会话的 agentMode
    }
  }

  async getEnvironmentDetails(includeFileDetails: boolean = false) {
    let details = "";
    const isInnerImage = (absolutePath: string) => {
      const ext = path.extname(absolutePath).toLowerCase();
      const isImage = [".png", ".jpeg", ".jpg"].includes(ext);
      if (isImage) {
        return absolutePath.startsWith(cwd);
      }
      return true;
    };
    // It could be useful for cline to know if the user went from one or no file to another between messages, so we always include this context
    details += "\n\n# VSCode Visible Files";
    const visibleFilePaths = vscode.window.visibleTextEditors
      ?.map(editor => editor.document?.uri?.fsPath)
      .filter(Boolean)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath))
      .map(absolutePath => path.relative(cwd, absolutePath));

    // Filter paths through clineIgnoreController
    const allowedVisibleFiles = visibleFilePaths
      .map(p => toPosixPath(p))
      .join("\n");

    if (allowedVisibleFiles) {
      details += `\n${allowedVisibleFiles}`;
    }
    else {
      details += "\n(No visible files)";
    }

    details += "\n\n# VSCode Open Tabs";
    const openTabPaths = vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .map(tab => (tab.input as vscode.TabInputText)?.uri?.fsPath)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath))
      .map(absolutePath => path.relative(cwd, absolutePath));

    // Filter paths through clineIgnoreController
    const allowedOpenTabs = openTabPaths
      .map(p => toPosixPath(p))
      .join("\n");

    if (allowedOpenTabs) {
      details += `\n${allowedOpenTabs}`;
    }
    else {
      details += "\n(No open tabs)";
    }

    const terminalStartTime = Date.now();
    const busyTerminals = await this.terminalManager.getRunningTerminalsInfo();
    const terminalDuration = Date.now() - terminalStartTime;
    console.log("getRunningTerminalsInfo", terminalDuration);

    let terminalDetails = "";
    if (busyTerminals.length > 0) {
      // terminals are cool, let's retrieve their output
      terminalDetails += "\n\n# Actively Running Terminals";
      for (const info of busyTerminals) {
        terminalDetails += `\n## Original command: \`${info.command}\`, pid: ${info.processId}`;
        terminalDetails += `\n### Terminal info:\n`;
        terminalDetails += `* ports info: ${JSON.stringify(info.ports)}\n`;
        terminalDetails += `* children info: ${JSON.stringify(info.children)}\n`;
        terminalDetails += `* fresh output: ${JSON.stringify(info.output && info.output?.length > 400 ? info.output?.slice(0, 200).concat(info.output.slice(-200)) : info.output)}\n`;
      }
    }

    if (terminalDetails) {
      details += terminalDetails;
    }

    // Add current time information with timezone
    const now = new Date();
    const formatter = new Intl.DateTimeFormat(undefined, {
      year: "numeric",
      month: "numeric",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      second: "numeric",
      hour12: true,
    });
    const timeZone = formatter.resolvedOptions().timeZone;
    const timeZoneOffset = -now.getTimezoneOffset() / 60; // Convert to hours and invert sign to match conventional notation
    const timeZoneOffsetStr = `${timeZoneOffset >= 0 ? "+" : ""}${timeZoneOffset}:00`;
    details += `\n\n# Current Time\n${formatter.format(now)} (${timeZone}, UTC${timeZoneOffsetStr})`;

    if (includeFileDetails) {
      details += `\n\n# Current Working Directory (${toPosixPath(cwd)}) Files\n`;
      const isDesktop = arePathsEqual(cwd, path.join(os.homedir(), "Desktop"));
      if (isDesktop) {
        // don't want to immediately access desktop since it would show permission popup
        details += "(Desktop files not shown automatically. Use list_files to explore if needed.)";
      }
      else {
        const [files, didHitLimit] = await listFiles(cwd, true, 200);
        const result = formatFilesList(cwd, files, didHitLimit);
        details += result;
      }
    }

    return `<environment_details>\n${details.trim()}\n</environment_details>`;
  }

  async getEnvironmentDetailsV2() {
    const isInnerImage = (absolutePath: string) => {
      const ext = path.extname(absolutePath).toLowerCase();
      const isImage = [".png", ".jpeg", ".jpg"].includes(ext);
      if (isImage) {
        return absolutePath.startsWith(cwd);
      }
      return true;
    };
    const visibleFilePaths = vscode.window.visibleTextEditors
      ?.map(editor => editor.document?.uri?.fsPath)
      .filter(Boolean)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath));
    const allowedVisibleFiles = visibleFilePaths
      .map(p => toPosixPath(p));

    const openTabPaths = vscode.window.tabGroups.all
      .flatMap(group => group.tabs)
      .map(tab => (tab.input as vscode.TabInputText)?.uri?.fsPath)
      .filter(Boolean).filter(absolutePath => isInnerImage(absolutePath))
      .map(absolutePath => absolutePath);

    // Filter paths through clineIgnoreController
    const allowedOpenTabs = openTabPaths
      .map(p => toPosixPath(p));

    return {
      visibleFiles: allowedVisibleFiles,
      openTabs: allowedOpenTabs,
    };
  }

  async executeCommandTool(command: string, is_background: boolean): Promise<ExecuteCommandResponse> {
    return await this.terminalManager.runCommand(command, is_background);
  }

  private decideCurrentTaskInterrupted(localMessages: InternalLocalMessage[]) {
    const lastMessage = localMessages.at(-1);
    if (
      (lastMessage?.type === "say" && lastMessage.say === "tool")
      || (lastMessage?.type === "ask" && lastMessage.ask === "tool")
      || (lastMessage?.type === "say" && lastMessage.say === "command")
      || (lastMessage?.type === "ask" && lastMessage.ask === "command")
      || (lastMessage?.type === "say" && lastMessage.say === "command_output")
      || (lastMessage?.type === "ask" && lastMessage.ask === "use_mcp_tool")
      || (lastMessage?.type === "say" && lastMessage.say === "use_mcp_tool_result")
      || (lastMessage?.type === "say" && lastMessage.say === "parse_figma_result")
      || (lastMessage?.type === "say" && lastMessage.say === "edit_file_result")
      || (lastMessage?.type === "say" && lastMessage.say === "checkpoint_created")
      || (lastMessage?.type === "say" && lastMessage.say === "api_req_started")
      || (lastMessage?.type === "ask" && lastMessage.ask === "command_status_check")
      || (lastMessage?.type === "say" && lastMessage.say === "command_status_check")
      || (lastMessage?.type === "say" && lastMessage.say === "command_status_check_result")
      // 普通的文本消息， 比如 "好的" "收到" "正在处理" 等，应当流转到 complete_result 等状态 因此也算 interrupted
      || (lastMessage?.type === "say" && lastMessage.say === "text")
      || (lastMessage?.partial === true)
    ) {
      return true;
    }
    return false;
  }

  async $showTaskWithId(sessionId: string) {
    // 切换 session 时， 切换历史记录
    const composerSessionStorageService = this.getService(ComposerSessionStorageService);
    // 使用带缓存的方法获取会话数据
    const sessionData = await composerSessionStorageService.getComposerSessionDataWithCache(sessionId);
    const shouldPostUpdate = sessionId !== this.sessionState.sessionId;

    // 如果切换到不同的会话，清理之前会话的性能日志记录器
    if (shouldPostUpdate && this.sessionState.chatId) {
      try {
        const performanceLogger = this.getBase(PerformanceLogger);

        // 在清理前上报性能日志
        await this.reportPerformanceLog(this.sessionState.chatId);

        performanceLogger.cleanup(this.sessionState.chatId);
      } catch (error) {
        this.logger.warn("Failed to cleanup performance logger when switching session", this.loggerScope, {
          err: error,
          value: {
            previousChatId: this.sessionState.chatId,
            newSessionId: sessionId,
          },
        });
      }
    }

    this.resetTaskState();
    Object.assign(this.sessionState, {
      sessionId,
      currentTaskInterrupted: sessionData ? this.decideCurrentTaskInterrupted(sessionData.localMessages) : false,
      indeterminatedWorkingSetEffects: [],
    });
    if (sessionData) {
      await this.postComposerStateUpdate(sessionData);
    }
    else if (shouldPostUpdate) {
      await this.postComposerStateUpdate();
    }
  }

  private resetTaskState() {
    this.sessionState = {
      sessionId: "",
      chatId: "",
      currentConversationDelayedUnsavedState: undefined,
      indeterminatedWorkingSetEffects: [],
      currentTaskInterrupted: false,
      localServiceConnectionLost: false,
      localServiceRelayDied: false,
      diagnosticsWhenNewTaskCreated: [],
      deltaMessageAccumulator: [],
    };
  }

  /**
 * 对比两个内容并返回差异块
 * @param originalContent 原始内容
 * @param modifiedContent 修改后的内容
 * @returns 差异块数组
 */
  private compareContents(originalContent: string, modifiedContent: string): DiffContent {
    // 使用diffLines比较内容
    const changes = diffLines(originalContent, modifiedContent);
    const withDiffAddLineNumber: number[] = [];
    const withDiffDeletedLineNumber: number[] = [];
    // let code = "";

    let modifiedLineNumber = 1; // 修改后的内容的当前行号（从1开始）
    let maxCharacterNumber = 0; // 当前行最大字符数

    // 遍历所有变化
    for (let i = 0; i < changes.length; i++) {
      const change = changes[i];
      // const value = change.value;
      // code += value;
      // 如果是添加的部分或者修改的部分
      if (change.added) {
        // 将新增的内容分割为行
        const addedLines = change.value.split("\n");
        // 如果最后一行是空行（来自换行符），则移除
        if (addedLines[addedLines.length - 1] === "") {
          addedLines.pop();
        }
        for (const line of addedLines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          withDiffAddLineNumber.push(modifiedLineNumber);
          modifiedLineNumber++;
        }
      }
      else if (change.removed) {
        const deletedLines = change.value.split("\n");

        if (deletedLines[deletedLines.length - 1] === "") {
          deletedLines.pop();
        }
        for (const line of deletedLines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          withDiffDeletedLineNumber.push(modifiedLineNumber);
          modifiedLineNumber++;
        }
      }
      else {
        // 未修改的部分，只需更新行号
        const lines = change.value.split("\n");
        if (lines[lines.length - 1] === "") {
          lines.pop();
        }
        for (const line of lines) {
          if (line.length > maxCharacterNumber) {
            maxCharacterNumber = line.length;
          }
          modifiedLineNumber++;
        }
      }
    }

    return {
      modifiedCode: modifiedContent,
      originalCode: originalContent,
      // addedLines: withDiffAddLineNumber,
      // deletedLines: withDiffDeletedLineNumber,
      // maxCharacterNumber,
    };
  }

  $addRuleFile(): void {
    this.getService(RulesService).$openProjectRules();
  }

  async clearTask() {
    const previousSessionId = this.sessionState.sessionId;
    const previousChatId = this.sessionState.chatId;

    // 清理之前会话的性能日志记录器
    if (previousChatId) {
      try {
        const performanceLogger = this.getBase(PerformanceLogger);

        // 在清理前上报性能日志
        await this.reportPerformanceLog(previousChatId);

        performanceLogger.cleanup(previousChatId);
      } catch (error) {
        this.logger.warn("Failed to cleanup performance logger", this.loggerScope, {
          err: error,
          value: { previousChatId },
        });
      }
    }

    const defaultChatModeConfig = this.getBase(ConfigManager).get(Config.DEFAULT_CHAT_MODE);
    // 根据默认模式配置来确定新会话的聊天模式
    let newChatMode = this.chatMode; // 默认保持现状

    if (defaultChatModeConfig === "agent") {
      // 如果设置为智能体模式，则重置为智能体
      newChatMode = "agent";
    }
    else if (defaultChatModeConfig === "ask") {
      // 如果设置为问答模式，则切换为问答
      newChatMode = "ask";
    }
    this.chatMode = newChatMode;
    // 如果是 follow，则保持现状，使用当前的 this.chatMode
    this.resetTaskState();
    const sessionId = this.sessionState.sessionId = generateCustomUUID();

    this.postComposerStateUpdate({
      localMessages: [],
      workspaceUri: "",
      sessionId,
      editingMessageTs: undefined,
      currentMessageTs: undefined,
      contextItems: [],
      // 需要获取配置
      chatMode: newChatMode,
      agentMode: this.agentMode,
    });
    // 保持 webview-UI 的 sessionId 和当前的会话 id 一致
    if (previousSessionId) {
      const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
      kwaipilotBinaryModule.request("assistant/agent/local", {
        type: "stop",
        params: {
          sessionId: previousSessionId,
        },
      });
    }
  }

  private get editFile() {
    return this.getService(WriteToFileService);
  }

  private get logger() {
    return this.getBase(LoggerManager);
  }

  $openImageInEditor(imgString: string): void {
    // 确保临时目录存在
    if (!existsSync(TEMP_PATH)) {
      mkdirSync(TEMP_PATH, { recursive: true });
    }

    const tmpPath = path.join(TEMP_PATH, `mermaid-${Date.now()}.png`);

    // 检查是否是data URL格式
    if (imgString.startsWith("data:image/png;base64,")) {
      // 提取base64数据
      const base64Data = imgString.replace("data:image/png;base64,", "");
      // 将base64转换为Buffer并写入文件
      const buffer = Buffer.from(base64Data, "base64");
      writeFileSync(tmpPath, buffer);
    }
    else if (imgString.startsWith("data:image/svg+xml;base64,")) {
      // 处理SVG的base64格式
      const base64Data = imgString.replace("data:image/svg+xml;base64,", "");
      const svgContent = Buffer.from(base64Data, "base64").toString("utf8");
      writeFileSync(tmpPath.replace(".png", ".svg"), svgContent, "utf8");
      const uri = vscode.Uri.file(tmpPath.replace(".png", ".svg"));
      vscode.commands.executeCommand("vscode.open", uri, this.viewColumn);
      return;
    }
    else {
      // 处理普通SVG字符串
      writeFileSync(tmpPath.replace(".png", ".svg"), imgString, "utf8");
      const uri = vscode.Uri.file(tmpPath.replace(".png", ".svg"));
      vscode.commands.executeCommand("vscode.open", uri, this.viewColumn);
      return;
    }

    const uri = vscode.Uri.file(tmpPath);
    vscode.commands.executeCommand("vscode.open", uri, this.viewColumn);
  }

  async $locateMentionNodeV2(node: MentionNodeV2Structure): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(t("locateMentionNodeV2.failedToLocate", { path: uri.toString() }));
        this.logger.error(`locateMentionNodeV2Failed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };
    const openImageInEditor = (uri: Uri): Thenable<any> => {
      return vscode.commands.executeCommand("vscode.open", uri, this.viewColumn).then(
        result => result,
        (e) => {
          vscode.window.showErrorMessage(t("locateMentionNodeV2.failedToOpenImage", { path: uri.toString() }));
          this.logger.error(`openImageFileFailed:${uri.toString()}`, this.loggerScope, {
            err: e,
          });
          throw e;
        },
      );
    };
    if (node.type === "remoteImage") {
      await openImageInEditor(vscode.Uri.parse(node.uri));
    }
    else if (node.type === "file" || node.type === "rule" || node.type === "remoteFile") {
      const doc = await openTextDocumentWithNotification(vscode.Uri.parse(node.uri));
      await vscode.window.showTextDocument(doc, this.viewColumn);
    }
    else if (node.type === "selection") {
      if (node.uri === "terminal://current") {
        vscode.commands.executeCommand("workbench.action.terminal.focus");
        return;
      }

      const doc = await openTextDocumentWithNotification(vscode.Uri.parse(node.uri));
      const editor = await vscode.window.showTextDocument(doc, this.viewColumn);
      const selectionStart = new vscode.Position(node.range.start.line, 0);
      const endLine = Math.min(node.range.end.line, doc.lineCount - 1);
      const selectionEnd = new vscode.Position(
        endLine,
        doc.lineAt(endLine).range.end.character,
      );
      editor.selections = [new vscode.Selection(selectionStart, selectionEnd)];

      // And the visible range jumps there too
      const range = new vscode.Range(selectionStart, selectionEnd);
      editor.revealRange(range);
    }
    else if (node.type === "tree"
      || node.type === "codebase"
      || node.type === "web"
      || node.type === "knowledge"
      || node.type === "slashCommand"
      || node.type === "dom"
      || node.type === "error"
      || node.type === "figma"
    ) {
      // 静默
    }
    else {
      const neverNode: never = node;
      throw new Error(`unsupported node type ${JSON.stringify(neverNode)}`);
    }
  }

  async $locateDiagnostic(uri: string, _diagnostic: SerializedDiagnostic): Promise<void> {
    const openTextDocumentWithNotification = (uri: Uri): Thenable<TextDocument> => {
      return vscode.workspace.openTextDocument(uri).then(e => e, (e) => {
        vscode.window.showErrorMessage(t("locateDiagnostic.failedToLocate", { path: uri.toString() }));
        this.logger.error(`locateDiagnosticFailed:${uri.toString()}`, this.loggerScope, {
          err: e,
        });
        throw e;
      });
    };

    const diagnostic = fromSerializedDiagnostic(_diagnostic);
    const doc = await openTextDocumentWithNotification(vscode.Uri.parse(uri));
    const editor = await vscode.window.showTextDocument(doc, this.viewColumn);
    editor.selections = [new vscode.Selection(diagnostic.range.start, diagnostic.range.end)];

    // And the visible range jumps there too
    editor.revealRange(diagnostic.range);
  }

  $setActiveSessionId(sessionId: string): void {
    this.getBase(WorkspaceStateManager).update(WorkspaceState.ACTIVE_COMPOSER_SESSION_ID, sessionId);
  }

  $getActiveSessionId(): string {
    return this.getBase(WorkspaceStateManager).get(WorkspaceState.ACTIVE_COMPOSER_SESSION_ID) || "";
  }

  async $openIDEPreview(path: string) {
    return await this.getService(UIPreviewService).openIDEPreview(path);
  }

  async $openBrowserPreview(path: string) {
    // const kwaipilotBinaryModule = this.getCore(KwaipilotBinaryModule);
    // return await kwaipilotBinaryModule.request("uiPreview/previewBrowser", { url: path });
    return await this.getService(UIPreviewService).openBrowserPreview(path);
  }

  $addFileToContext(uri: string): void {
    // 将 file:// 路径字符串转换为 vscode.Uri
    const fileUri = vscode.Uri.parse(uri);
    this.handleAddFileToContext(fileUri);
  }

  $setMcpCanCommandProceededByUserStatus(status: boolean): void {
    vscode.commands.executeCommand("setContext", `${APP_NAME}.mcpCanCommandProceededByUser`, status);
  }

  async $getHistoryHumainMessage(direction: "pre" | "next"): Promise<{
    text?: string;
    editorState?: SerializedEditorState;
    contextItems?: MentionNodeV2Structure[];
  }> {
    // 调用 ComposerHistoryStorageService 获取历史消息
    return await this.getService(ComposerHistoryStorageService).getHistoryHumainMessage(direction);
  }

  /**
   * 保存用户消息到历史记录
   * @param params 保存参数
   */
  async $saveUserMessageToHistory(params: {
    text: string;
    editorState: SerializedEditorState;
    contextItems: MentionNodeV2Structure[];
  }): Promise<void> {
    const { text, editorState, contextItems } = params;

    // 调用 ComposerHistoryStorageService 保存用户消息
    await this.getService(ComposerHistoryStorageService).saveUserMessage(text, editorState, contextItems);
  }

  private get viewColumn() {
    return this.getBase(WorkspaceStateManager).get(WorkspaceState.FULL_MODE_NEW_WINDOW) ? vscode.ViewColumn.One : vscode.ViewColumn.Active;
  }

  private get httpClient() {
    return this.getBase(Api);
  }

  // ui Preview 工具是否启用
  private get uiPreviewEnable() {
    try {
      return vscode.env.uiKind === vscode.UIKind.Desktop && !vscode.env.remoteName;
    }
    catch (err) {
      return false;
    }
  }

  private get replaceService() {
    return this.getService(ReplaceFileService);
  }
}

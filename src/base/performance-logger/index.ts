import { createLogger, transports, format, Logger } from "winston";
import path from "path";
import fs from "fs";
import { BaseModule } from "..";
import { ContextManager } from "../context-manager";
import { LOG_PATH } from "../../common/const";
import { WebloggerManager } from "../weblogger";

export const PERF_START_FLAG='chat-perf-start';
export const PERF_END_FLAG='chat-perf-end';
/**
 * 性能日志记录器
 * 专门用于记录各个阶段的性能数据到独立的日志文件
 */
export class PerformanceLogger extends BaseModule {
  private loggers: Map<string, Logger> = new Map();
  private endedChats: Set<string> = new Set(); // 缓存已结束的聊天ID
  private perfTimers: Map<string, number> = new Map(); // 存储性能计时器的开始时间

  constructor(ext: ContextManager) {
    super(ext);
  }

  /**
   * 检查指定 chatId 的日志是否已经结束（包含 PERF_END_FLAG）
   * @param chatId 聊天ID
   * @returns 是否已结束
   */
  private isLogEnded(chatId: string): boolean {
    // 先检查缓存
    if (this.endedChats.has(chatId)) {
      return true;
    }

    // 检查日志文件
    const logFilePath = path.join(LOG_PATH, `perf-agent-${chatId}.log`);
    if (!fs.existsSync(logFilePath)) {
      return false;
    }

    try {
      const logContent = fs.readFileSync(logFilePath, 'utf8');
      const hasEndFlag = logContent.includes(`"stage":"${PERF_END_FLAG}"`);

      if (hasEndFlag) {
        this.endedChats.add(chatId);
        return true;
      }

      return false;
    } catch (error) {
      console.warn(`Failed to check log end flag for chatId: ${chatId}`, error);
      return false;
    }
  }

  /**
   * 标记日志结束，写入 PERF_END_FLAG
   * @param chatId 聊天ID
   */
  public markLogEnded(chatId: string): void {
    if (this.isLogEnded(chatId)) {
      return; // 已经结束，不重复标记
    }

    const logger = this.getLogger(chatId);
    logger.info(PERF_END_FLAG, {
      endTime: new Date().toISOString(),
      timestamp: Date.now(),
    });

    // 添加到缓存
    this.endedChats.add(chatId);
  }

  /**
   * 获取或创建指定 chatId 的性能日志记录器
   * @param chatId 聊天ID
   * @returns Winston Logger 实例
   */
  private getLogger(chatId: string): Logger {
    if (this.loggers.has(chatId)) {
      return this.loggers.get(chatId)!;
    }

    const logger = createLogger({
      format: format.combine(
        format.timestamp(),
        format.printf(({ timestamp, message, ...meta }) => {
          function formatDateString(dateString: string): string {
            const beijingOffset = 8 * 60;
            const date = new Date(new Date(dateString).getTime() + beijingOffset * 60 * 1000);
            const yy = String(date.getUTCFullYear()).slice(-2);
            const mm = String(date.getUTCMonth() + 1).padStart(2, "0");
            const dd = String(date.getUTCDate()).padStart(2, "0");
            const hh = String(date.getUTCHours()).padStart(2, "0");
            const min = String(date.getUTCMinutes()).padStart(2, "0");
            const ss = String(date.getUTCSeconds()).padStart(2, "0");
            const ms = String(date.getUTCMilliseconds()).padStart(3, "0");

            return `${yy}/${mm}/${dd} ${hh}:${min}:${ss}.${ms}`;
          }
          const localtime = formatDateString(timestamp);

          const data = {
            localtime,
            chatId,
            ...meta,
            message,
          };

          return JSON.stringify(data);
        }),
      ),
      transports: [
        new transports.File({
          filename: path.join(LOG_PATH, `perf-agent-${chatId}.log`),
          level: "info",
          maxsize: 10 * 1024 * 1024, // 10MB
          maxFiles: 5,
        }),
      ],
    });

    this.loggers.set(chatId, logger);
    return logger;
  }

  /**
   * 记录性能数据
   * @param chatId 聊天ID
   * @param stage 阶段名称
   * @param duration 耗时（毫秒）
   * @param metadata 额外的元数据
   */
  public logPerformance(
    chatId: string,
    stage: string,
    duration: number,
    metadata?: Record<string, any>
  ): void {
    // 检查日志是否已结束
    if (this.isLogEnded(chatId)) {
      console.debug(`Performance log for chatId ${chatId} has ended, skipping logPerformance`);
      return;
    }

    const logger = this.getLogger(chatId);

    logger.info("performance", {
      stage,
      duration,
      ...metadata,
    });
  }

  /**
   * 记录性能时间点
   * @param chatId 聊天ID
   * @param stage 阶段名称
   * @param timestamp 时间戳
   * @param metadata 额外的元数据
   */
  public logPerformanceTimestamp(
    chatId: string,
    stage: string,
    timestamp: number,
    metadata?: Record<string, any>
  ): void {
    // 检查日志是否已结束
    if (this.isLogEnded(chatId)) {
      console.debug(`Performance log for chatId ${chatId} has ended, skipping logPerformanceTimestamp`);
      return;
    }

    const logger = this.getLogger(chatId);

    logger.info("timestamp", {
      stage,
      timestamp,
      time: new Date(timestamp).toISOString(),
      ...metadata,
    });
  }

  /**
   * 记录完整的性能统计
   * @param chatId 聊天ID
   * @param messageType 消息类型
   * @param performanceTimes 性能时间数组
   * @param totalDuration 总耗时
   */
  public logPerformanceSummary(
    chatId: string,
    messageType: string,
    performanceTimes: {time: number, label: string; durtation?: number}[],
    totalDuration: number
  ): void {
    // 检查日志是否已结束
    if (this.isLogEnded(chatId)) {
      console.debug(`Performance log for chatId ${chatId} has ended, skipping logPerformanceSummary`);
      return;
    }

    const logger = this.getLogger(chatId);
    
    // 记录总体统计
    logger.info("summary", {
      messageType,
      totalDuration,
      stages: performanceTimes.map(item => ({
        label: item.label,
        duration: item.durtation,
      })),
    });

    // 记录每个阶段的详细信息
    performanceTimes.forEach((item, index) => {
      if (item.durtation !== undefined) {
        this.logPerformance(chatId, `${messageType}_${item.label}`, item.durtation, {
          messageType,
          timestamp: item.time,
        });
      }
    });
  }

  /**
   * 清理指定 chatId 的日志记录器
   * @param chatId 聊天ID
   */
  public cleanup(chatId: string): void {
    // 在清理前标记日志结束
    this.markLogEnded(chatId);

    const logger = this.loggers.get(chatId);
    if (logger) {
      // 关闭所有 transports
      logger.transports.forEach(transport => {
        if (transport.close) {
          transport.close();
        }
      });
      this.loggers.delete(chatId);
    }

    // 从缓存中移除（下次访问时会重新检查文件）
    // 注意：这里不移除 endedChats 缓存，因为日志确实已经结束了
  }

  /**
   * 清理所有日志记录器
   */
  public cleanupAll(): void {
    this.loggers.forEach((_logger, chatId) => {
      this.cleanup(chatId);
    });
  }

  /**
   * 上报性能日志文件内容到 weblogger
   * @param chatId 聊天ID
   * @param options 上报选项
   */
  public async reportPerfLog(
    chatId: string,
    options?: {
      maxLines?: number; // 最大上报行数，默认100
      includeRawData?: boolean; // 是否包含原始日志数据，默认false
    }
  ): Promise<void> {
    try {
      const { maxLines = 100, includeRawData = false } = options || {};
      const logFilePath = path.join(LOG_PATH, `perf-agent-${chatId}.log`);

      // 检查文件是否存在
      if (!fs.existsSync(logFilePath)) {
        console.warn(`Performance log file not found: ${logFilePath}`);
        return;
      }

      // 读取日志文件
      const logContent = fs.readFileSync(logFilePath, 'utf8');
      const lines = logContent.trim().split('\n').filter(line => line.trim());

      if (lines.length === 0) {
        console.warn(`Performance log file is empty: ${logFilePath}`);
        return;
      }

      // 解析日志数据并生成统计信息
      const stats = this.analyzeLogData(lines);

      // 准备上报数据
      const reportData: any = {
        chatId,
        analysisTime: new Date().toISOString(),
        ...stats,
      };

      // 如果需要包含原始数据，添加最近的日志条目
      if (includeRawData) {
        const recentLines = lines.slice(-Math.min(maxLines, lines.length));
        reportData.recentLogs = recentLines.map(line => {
          try {
            return JSON.parse(line);
          } catch (e) {
            return { rawLine: line, parseError: true };
          }
        });
      }

      // 通过 weblogger 上报，使用现有的 agent_chat_time_info key
      const weblogger = this.getBase(WebloggerManager);
      weblogger.$reportUserAction({
        key: "agent_chat_time_info",
        type: undefined,
        chatId,
        content: JSON.stringify(reportData),
      });

    } catch (error) {
      console.error(`Failed to report performance log for chatId: ${chatId}`, error);
    }
  }

  /**
   * 分析日志数据，生成统计信息
   * @param lines 日志行数组
   * @returns 统计信息
   */
  private analyzeLogData(lines: string[]): {
    totalOperations: number;
    timeRange: { start?: string; end?: string };
    logs: Array<{ messageType: string; totalDuration: number; stage?: string }>;
  } {
    const durations: Record<string, number[]> = {};
    let totalOperations = 0;
    let startTime: string | undefined;
    let endTime: string | undefined;

    lines.forEach(line => {
      try {
        const logEntry = JSON.parse(line);

       
        // 收集耗时数据
        if (logEntry.messageType && logEntry.totalDuration !== undefined) {
          if (!durations[logEntry.messageType]) {
            durations[logEntry.messageType] = [];
          }
          durations[logEntry.messageType].push(logEntry.totalDuration);
        }

        // 记录时间范围
        if (logEntry.localtime) {
          if (!startTime || logEntry.localtime < startTime) {
            startTime = logEntry.localtime;
          }
          if (!endTime || logEntry.localtime > endTime) {
            endTime = logEntry.localtime;
          }
        }
      } catch (e) {
        // 忽略解析错误的行
      }
    });
  
    const logs=lines.map(item=>JSON.parse(item));

    return {
      totalOperations,
      timeRange: { start: startTime, end: endTime },
      logs:logs.map(({messageType, totalDuration,duration, stage})=>({
        messageType,
        totalDuration,
        duration,
        stage
      }))
    };
  }

  /**
   * 开始性能计时
   * @param key 计时器的唯一标识
   */
  public perfStart(key: string): void {
    this.perfTimers.set(key, Date.now());
  }

  /**
   * 结束性能计时并返回耗时
   * @param key 计时器的唯一标识
   * @returns 耗时（毫秒），如果没有找到对应的开始时间则返回 -1
   */
  public perfEnd(key: string): number {
    const startTime = this.perfTimers.get(key);
    if (startTime === undefined) {
      console.warn(`Performance timer '${key}' was not started`);
      return -1;
    }

    const duration = Date.now() - startTime;
    this.perfTimers.delete(key); // 清理已完成的计时器
    return duration;
  }
}
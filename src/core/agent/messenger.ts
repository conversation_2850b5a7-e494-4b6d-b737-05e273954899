import { AgentCommonMessage, IProtocol, IMessenger, type Message, IdeCommonMessage } from "./types";
import { ChildProcessWithoutNullStreams } from "child_process";
import { v4 as uuidv4 } from "uuid";
import { Project } from "../project";
import * as vscode from "vscode";
import * as net from "net";
import { version } from "../../../package.json";
import { BufferDataHandle } from "./util";
import { APP_NAME } from "shared/lib/const";
import { throttle } from "lodash";

type ErrorHandler<T extends IProtocol, U extends IProtocol> = Parameters<IMessenger<T, U>["onError"]>[0];

class IdeCommonMessageCache {
  constructor(private project: Project) {
  }

  cached: IdeCommonMessage = this.getCommonMessage();

  private getCommonMessage(): IdeCommonMessage {
    const repoPath = this.project.getRepoPath() || "";
    const gitUrl = this.project.getRemoteOriginUrl(repoPath) || "";
    return {
      version: vscode.version,
      platform: "vscode",
      pluginVersion: version,
      cwd:
        vscode.workspace.workspaceFolders?.map(folder => folder.uri.fsPath).at(0)
        ?? "",
      repo: {
        git_url: gitUrl,
        dir_path: repoPath,
        commit: this.project.getCurrentCommit() || "",
      },
    };
  }

  private throttledUpdate = throttle(() => {
    this.cached = this.getCommonMessage();
  }, 1000, {
    leading: true,
    trailing: true,
  });

  get() {
    this.throttledUpdate();
    return this.cached;
  }
}

class IPCMessengerBase<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  _sendMsg(_message: Message) {
    throw new Error("Not implemented");
  }

  lastMessageTime = 0;
  typeListeners = new Map<keyof ToProtocol, ((message: Message) => any)[]>();
  idListeners = new Map<string, (message: Message) => any>();

  private readonly dataHandler = new BufferDataHandle();

  private triggerError(e: Error, details?: string) {
    this._onErrorHandlers.forEach((handler) => {
      handler(e, details);
    });
  }

  private _handleLine(line: string) {
    try {
      const msg: Message = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error("Invalid message sent: " + JSON.stringify(msg));
      }

      // Call handler and respond with return value
      const listeners = this.typeListeners.get(msg.messageType as any);
      listeners?.forEach(async (handler) => {
        try {
          console.log("msg", msg);
          const response = await handler(msg);
          console.log("response", response);
          if (response && typeof response[Symbol.asyncIterator] === "function") {
            for await (const update of response) {
              this.send(msg.messageType, update, msg.messageId);
            }
            this.send(msg.messageType, { done: true }, msg.messageId);
          }
          else {
            console.log("response", response);
            this.send(msg.messageType, response, msg.messageId);
          }
        }
        catch (e: any) {
          console.warn(`Error running handler for "${msg.messageType}": `, e);
          this.triggerError(e);
        }
      });

      // Call handler which is waiting for the response, nothing to return
      this.idListeners.get(msg.messageId)?.(msg);
    }
    catch (e: any) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + "..." + line.substring(line.length - 100);
      }
      console.error("Error parsing line: ", truncatedLine, e);
      this.triggerError(e, "Error parsing line: " + line);
      return;
    }
  }

  protected _handleData(data: Buffer) {
    this.lastMessageTime = Date.now();
    this.dataHandler.handleData(data, line => this._handleLine(line));
  }

  checkIsRunning(): boolean {
    return Date.now() - this.lastMessageTime < 1000 * 5;
  }

  private _onErrorHandlers: ErrorHandler<ToProtocol, FromProtocol>[] = [];

  onError(handler: ErrorHandler<ToProtocol, FromProtocol>) {
    this._onErrorHandlers.push(handler);
  }

  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]> {
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: Message) => {
        resolve(msg.data);
        this.idListeners.delete(messageId);
      };
      this.idListeners.set(messageId, handler);
      this.send(messageType, data, messageId);
    });
  }

  mock(data: any) {
    const d = JSON.stringify(data);
    this._handleData(Buffer.from(d));
  }

  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string {
    messageId = messageId ?? uuidv4();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId,
    };
    this._sendMsg(msg);
    return messageId;
  }

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0]): ToProtocol[T][1] {
    return this.typeListeners.get(messageType)?.[0]?.({
      messageId: uuidv4(),
      messageType: messageType as string,
      data,
    });
  }

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => Promise<ToProtocol[T][1]> | ToProtocol[T][1],
  ): void {
    if (!this.typeListeners.has(messageType)) {
      this.typeListeners.set(messageType, []);
    }
    this.typeListeners.get(messageType)?.push(handler);
  }

  dispose() {
    this.typeListeners.clear();
    this.idListeners.clear();
  }
}
export class CoreBinaryMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  extends IPCMessengerBase<ToProtocol, FromProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  constructor(private readonly subprocess: ChildProcessWithoutNullStreams) {
    super();
    this.subprocess.stdout.on("data", (data) => {
      console.log(`[info] ${APP_NAME} received data from core:`, data.toString() + "\n");
      this._handleData(data);
    });
    this.subprocess.stdout.on("close", () => {
      console.log(`[info] ${APP_NAME} core exited`);
    });
    this.subprocess.stdin.on("close", () => {
      console.log(`[info] ${APP_NAME} core exited`);
    });
  }

  getAgentVersion(): string {
    return "1.0.0";
  }

  getCommonMessage(): AgentCommonMessage {
    return {
      version: this.getAgentVersion(),
    };
  }

  _sendMsg(msg: Message) {
    console.log("[info] Sending message to core:", msg);
    if (!msg.common) {
      msg.common = this.getCommonMessage();
    }
    const d = JSON.stringify(msg);
    this.subprocess.stdin.write(d + "\r\n");
  }
}
export class TcpMessenger<ToProtocol extends IProtocol, FromProtocol extends IProtocol>
  implements IMessenger<ToProtocol, FromProtocol> {
  private host: string = "127.0.0.1";
  private socket: net.Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000; // 1 second
  private reconnectTimer: NodeJS.Timeout | null = null;
  private readonly dataHandler = new BufferDataHandle();
  private lastMessageTime = 0;
  typeListeners = new Map<keyof ToProtocol, ((message: Message) => any)[]>();
  idListeners = new Map<string, (message: Message) => any>();
  isConnected = false;
  requestPendding: {
    messageType: keyof FromProtocol;
    data: FromProtocol[keyof FromProtocol][0];
    messageId: string;
  }[] = [];

  constructor(private project: Project) {
  }

  private ideCommonMessageCache = new IdeCommonMessageCache(this.project);

  checkIsRunning(): boolean {
    return Date.now() - this.lastMessageTime < 1000 * 5;
  }

  private triggerError(e: Error, details?: string) {
    this._onErrorHandlers.forEach((handler) => {
      handler(e, details);
    });
  }

  public connect() {
    if (this.socket) {
      this.socket.destroy();
      this.socket = null;
    }

    const client = new net.Socket();

    client.on("connect", () => {
      console.log("Connected to server");
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.socket = client;

      // Process any pending requests
      this.requestPendding.forEach((request) => {
        this.send(request.messageType, request.data, request.messageId);
      });
      this.requestPendding = [];
    });

    client.on("data", (data: Buffer) => {
      this.lastMessageTime = Date.now();
      this._handleData(data);
    });

    client.on("end", () => {
      console.log("Disconnected from server");
      this.isConnected = false;
      this.handleDisconnect();
    });

    client.on("error", (err: Error) => {
      console.error("Socket error:", err);
      this.isConnected = false;
      this.handleDisconnect();
    });
    const port: number = process.env.KWAIPILOT_AGENT_PORT ? parseInt(process.env.KWAIPILOT_AGENT_PORT) : 30001;

    client.connect(port, this.host);
  }

  private handleDisconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
      }

      this.reconnectTimer = setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
    else {
      console.error("Max reconnection attempts reached");
      this.triggerError(new Error("Failed to establish connection after maximum attempts"));
    }
  }

  private _onErrorHandlers: ErrorHandler<ToProtocol, FromProtocol>[] = [];

  onError(handler: ErrorHandler<ToProtocol, FromProtocol>) {
    this._onErrorHandlers.push(handler);
  }

  public async awaitConnection() {
    while (!this.socket) {
      await new Promise(resolve => setTimeout(resolve, 100));
    }
  }

  private _handleLine(line: string) {
    try {
      // console.log(`tcp data in---- ${line}`);
      const msg: Message = JSON.parse(line);
      if (msg.messageType === undefined || msg.messageId === undefined) {
        throw new Error("Invalid message sent: " + JSON.stringify(msg));
      }

      // Call handler and respond with return value
      const listeners = this.typeListeners.get(msg.messageType as any);
      const s = Date.now();
      listeners?.forEach(async (handler) => {
        try {
          const response = await handler(msg);
          if (response && typeof response[Symbol.asyncIterator] === "function") {
            for await (const update of response) {
              this.send(msg.messageType, update, msg.messageId);
            }
            this.send(msg.messageType, { done: true }, msg.messageId);
          }
          else {
            this.send(msg.messageType, response || {}, msg.messageId);
          }
        }
        catch (e: any) {
          console.warn(`Error running handler for "${msg.messageType}": `, e);
          this.triggerError(e);
          this.send(msg.messageType, { status: "error", message: e.message }, msg.messageId);
        } finally {
          console.log(`single msg cost time: ${msg.messageType}`, Date.now() - s);
        }
      });

      // Call handler which is waiting for the response, nothing to return
      this.idListeners.get(msg.messageId)?.(msg);
    }
    catch (e: any) {
      let truncatedLine = line;
      if (line.length > 200) {
        truncatedLine = line.substring(0, 100) + "..." + line.substring(line.length - 100);
      }
      console.error("Error parsing line: ", truncatedLine, e);
      this.triggerError(e, "Error parsing line: " + line);
      return;
    }
  }

  protected _handleData(data: Buffer) {
    this.dataHandler.handleData(data, line => this._handleLine(line));
  }

  send<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0], messageId?: string): string {
    messageId = messageId ?? uuidv4();
    const s = Date.now();
    const msg: Message = {
      messageType: messageType as string,
      data,
      messageId,
      common: this.ideCommonMessageCache.get(),
    };

    this.socket?.write(JSON.stringify(msg) + "\r\n");
    console.log(`send msg cost time: ${messageType as string}`, Date.now() - s);
    return messageId;
  }

  on<T extends keyof ToProtocol>(
    messageType: T,
    handler: (message: Message<ToProtocol[T][0]>) => ToProtocol[T][1],
  ): void {
    if (!this.typeListeners.has(messageType)) {
      this.typeListeners.set(messageType, []);
    }
    this.typeListeners.get(messageType)?.push(handler);
  }

  invoke<T extends keyof ToProtocol>(messageType: T, data: ToProtocol[T][0]): ToProtocol[T][1] {
    return this.typeListeners.get(messageType)?.[0]?.({
      messageId: uuidv4(),
      messageType: messageType as string,
      data,
    });
  }

  request<T extends keyof FromProtocol>(messageType: T, data: FromProtocol[T][0]): Promise<FromProtocol[T][1]> {
    const messageId = uuidv4();
    return new Promise((resolve) => {
      const handler = (msg: Message) => {
        resolve(msg.data);
        this.idListeners.delete(messageId);
      };
      this.idListeners.set(messageId, handler);
      if (this.isConnected) {
        this.send(messageType, data, messageId);
      }
      else {
        console.error("Not connected to server");
        this.requestPendding.push({ messageType, data, messageId });
      }
    });
  }

  dispose() {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    this.socket?.destroy();
    this.socket = null;
    this.isConnected = false;
  }
}

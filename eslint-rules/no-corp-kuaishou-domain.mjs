export default {
  meta: {
    type: "problem",
    docs: {
      description: "【编码提示】检测代码中是否存在 corp.kuaishou.com 相关域名，提醒注意外网访问支持",
      category: "Best Practices",
      recommended: true,
    },
    fixable: null,
    schema: [],
    messages: {
      noCorpKuaishouDomain: "【编码提示】检测到 corp.kuaishou.com 相关域名，需注意在外网访问支持",
    },
  },
  create(context) {
    /**
     * 检查字符串是否包含 corp.kuaishou.com 相关域名
     * @param {string} value - 字符串值
     * @returns {boolean} 是否包含相关域名
     */
    function containsCorpKuaishouDomain(value) {
      if (typeof value !== "string") return false;

      // 检查是否包含 corp.kuaishou.com 相关域名
      const domainPatterns = [
        /corp\.kuaishou\.com/i,
        /kuaishou\.corp/i,
        /cdnfile\.corp\.kuaishou\.com/i,
      ];

      return domainPatterns.some(pattern => pattern.test(value));
    }

    /**
     * 检查 AST 节点中的字符串字面量
     * @param {Object} node - AST 节点
     */
    function checkStringLiteral(node) {
      if (node.type === "Literal" && typeof node.value === "string") {
        if (containsCorpKuaishouDomain(node.value)) {
          context.report({
            node,
            messageId: "noCorpKuaishouDomain",
          });
        }
      }
    }

    /**
     * 检查模板字面量中的字符串
     * @param {Object} node - AST 节点
     */
    function checkTemplateLiteral(node) {
      if (node.type === "TemplateLiteral") {
        // 检查模板字面量的 quasis（静态字符串部分）
        node.quasis.forEach((quasi) => {
          if (quasi.value && containsCorpKuaishouDomain(quasi.value.raw)) {
            context.report({
              node: quasi,
              messageId: "noCorpKuaishouDomain",
            });
          }
        });
      }
    }

    return {
      // 检查字符串字面量
      Literal(node) {
        checkStringLiteral(node);
      },

      // 检查模板字面量
      TemplateLiteral(node) {
        checkTemplateLiteral(node);
      },

      // 检查 JSX 属性中的字符串
      JSXAttribute(node) {
        if (node.value && node.value.type === "Literal") {
          checkStringLiteral(node.value);
        }
      },

      // 检查对象属性中的字符串
      Property(node) {
        if (node.value && node.value.type === "Literal") {
          checkStringLiteral(node.value);
        }
      },

      // 检查数组元素中的字符串
      ArrayExpression(node) {
        node.elements.forEach((element) => {
          if (element && element.type === "Literal") {
            checkStringLiteral(element);
          }
        });
      },

      // 检查函数参数中的字符串
      FunctionDeclaration(node) {
        node.params.forEach((param) => {
          if (param && param.type === "Literal") {
            checkStringLiteral(param);
          }
        });
      },

      // 检查箭头函数参数中的字符串
      ArrowFunctionExpression(node) {
        node.params.forEach((param) => {
          if (param && param.type === "Literal") {
            checkStringLiteral(param);
          }
        });
      },
    };
  },
};

//  token 配置文件 webview-ui/src/style/designToken.ts
const oldTokens = {
  colorTextCommonPrimary: "#20242A",
  colorTextCommonSecondary: "#636C76",
  colorTextCommonTertiary: "#95A1AC",
  colorTextCommonDisable: "#A9B3BE",
  colorTextBrandDefault: "#118DFF",
  colorTextBrandHover: "#359EFF",
  colorIconCommonPrimary: "#20242A",
  colorIconCommonSecondary: "#636C76",
  colorIconCommonTertiary: "#95A1AC",
  colorIconCommonDisable: "#A9B3BE",
  colorIconBrandDefault: "#118DFF",
  colorIconBrandHover: "#359EFF",
  colorBorderCommon: "#E1EAF5",
  colorBorderHorizontal: "#E7EEF7",
  colorBorderVertical: "#E1E2E3",
  colorBorderBar: "#E8F1FF",
  colorBorderNormal: "#D6DCE3",
  colorBgUserDefault: "#D3EBFF",
  colorBgSystemDefault: "#FFFFFF",
  colorBgControlsHover: "#E2E9F2",
  colorBgSelected: "#F0F6FF",
  colorBgHover: "#F3F8FF",
  colorBgBrandActive: "rgba(17, 141, 255, 0.08)",
  colorBgFill: "#FFFFFF",
  colorBgCodeCard: "#FFFFFF",
  colorBgScrollbarDefault: "#DADFE7",
  colorBgCodeBar: "#F4FAFD",
  colorBgCodeContent: "#1A222E",
  colorBgBar: "#F6FBFF",
  colorBgInputFill: "#FFF",
  colorInputTextPlaceholder: "#70777F",
  colorInputBorderNormal: "#D6DCE3",
  colorInputBorderBar: "#E8F1FF",
  colorInputBgBar: "#F6FBFF",
  colorInputBgInputFill: "#FFFFFF",
  colorSendHover: "#E1E5EC",
  colorInputBgSendDisable: "#DADFE7",
  colorInputIconSendDisable: "#FFFFFF",
  colorTagTextKwaipilot: "rgba(129, 155, 189, 0.8)",
  colorTagTextClaude: "rgba(180, 142, 129, 0.8)",
  colorTagTextGpt: "rgba(129, 189, 143, 0.8)",
  // 指令块
  colorCommandText: "rgba(99, 108, 118, 1)",
  colorCommandBg: "rgba(239, 247, 253, 1)",

  colorBgDiffAdded: "#d3e3b1",
  colorBgDiffRemoved: "#fdadad",
};

const oldTokensKeys = Object.keys(oldTokens);
// colorTextCommonPrimary -> text-common-primary
const oldTWKeys = oldTokensKeys.map(convertColorToTextClass);

function convertColorToTextClass(colorKey) {
  // 移除 color 前缀
  const withoutColorPrefix = colorKey.replace(/^color/, "");
  // 将驼峰命名转为短横线连接
  const kebabCase = withoutColorPrefix
    .replace(/([a-z0-9])([A-Z])/g, "$1-$2")
    .toLowerCase();
  return kebabCase;
}

export default {
  meta: {
    type: "suggestion",
    docs: {
      description:
        "【编码提示】请不要直接使用 '{{className}}' 类名，应该使用 VSCode 设计 token 替代 https://vscode.js.cn/api/references/theme-color#base-colors",
      category: "Best Practices",
      recommended: true,
    },
    fixable: "code",
    schema: [],
    messages: {
      noTextCommonPrimaryClass:
        "【编码提示】请不要直接使用 '{{className}}'  类名，应该使用 VSCode 设计 token  替代 https://vscode.js.cn/api/references/theme-color#base-colors",
    },
  },
  create(context) {
    // 辅助函数：检查节点中是否包含禁用的类名
    function checkNodeForProhibitedClasses(node) {
      // 处理字符串字面量
      if (node && node.type === "Literal" && typeof node.value === "string") {
        let matchKey = "";
        const hasMatch = oldTWKeys.some((key) => {
          const match = node.value.includes(key);
          if (match) {
            matchKey = key;
          }
          return match;
        });

        if (hasMatch) {
          context.report({
            node,
            messageId: "noTextCommonPrimaryClass",
            data: {
              className: matchKey,
            },
          });
        }
        return;
      }

      // 处理三元运算符表达式
      if (node && node.type === "ConditionalExpression") {
        checkNodeForProhibitedClasses(node.consequent); // 检查条件为真的分支
        checkNodeForProhibitedClasses(node.alternate); // 检查条件为假的分支
        return;
      }

      // 处理数组表达式
      if (node && node.type === "ArrayExpression") {
        node.elements.forEach((element) => {
          checkNodeForProhibitedClasses(element);
        });
        return;
      }

      // 处理函数调用，例如clsx或classNames
      if (node && node.type === "CallExpression"
        && (node.callee.name === "clsx" || node.callee.name === "classNames")) {
        node.arguments.forEach((arg) => {
          checkNodeForProhibitedClasses(arg);
        });
        return;
      }
    }

    return {
      JSXAttribute(node) {
        if (node.name.name === "className" && node.value) {
          // 处理直接的字符串字面量
          if (node.value.type === "Literal" && typeof node.value.value === "string") {
            checkNodeForProhibitedClasses(node.value);
          }

          // 处理JSXExpressionContainer
          if (node.value.type === "JSXExpressionContainer") {
            checkNodeForProhibitedClasses(node.value.expression);
          }
        }
      },
    };
  },
};

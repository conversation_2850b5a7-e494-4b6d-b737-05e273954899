#!/usr/bin/env node

/**
 * 简单的性能日志测试脚本
 * 用于验证性能日志功能是否正常工作
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 模拟日志路径
const LOG_PATH = path.resolve(os.homedir(), '.kwaipilot/logs');

// 测试数据
const testChatId = 'test-chat-12345';
const testLogFile = path.join(LOG_PATH, `perf-agent-${testChatId}.log`);

console.log('🧪 性能日志功能测试');
console.log('==================');
console.log(`日志目录: ${LOG_PATH}`);
console.log(`测试文件: ${testLogFile}`);
console.log('');

// 检查日志目录是否存在
if (!fs.existsSync(LOG_PATH)) {
    console.log('❌ 日志目录不存在，请先运行 KwaiPilot 扩展');
    process.exit(1);
}

// 检查是否有性能日志文件
const perfLogFiles = fs.readdirSync(LOG_PATH)
    .filter(file => file.startsWith('perf-agent-') && file.endsWith('.log'));

console.log(`📁 找到 ${perfLogFiles.length} 个性能日志文件:`);
perfLogFiles.forEach(file => {
    const filePath = path.join(LOG_PATH, file);
    const stats = fs.statSync(filePath);
    const chatId = file.replace('perf-agent-', '').replace('.log', '');
    
    console.log(`  - ${file}`);
    console.log(`    聊天ID: ${chatId}`);
    console.log(`    大小: ${(stats.size / 1024).toFixed(2)} KB`);
    console.log(`    修改时间: ${stats.mtime.toLocaleString()}`);
    console.log('');
});

if (perfLogFiles.length === 0) {
    console.log('ℹ️  没有找到性能日志文件');
    console.log('   请在 KwaiPilot 中执行一些操作（如发送消息）来生成性能日志');
    console.log('');
} else {
    // 分析第一个日志文件
    const firstLogFile = path.join(LOG_PATH, perfLogFiles[0]);
    console.log(`🔍 分析日志文件: ${perfLogFiles[0]}`);
    console.log('==========================================');
    
    try {
        const logContent = fs.readFileSync(firstLogFile, 'utf8');
        const lines = logContent.trim().split('\n').filter(line => line.trim());
        
        console.log(`总日志条数: ${lines.length}`);
        
        // 统计不同类型的日志
        const messageTypes = {};
        const operationTypes = {};
        
        lines.forEach(line => {
            try {
                const logEntry = JSON.parse(line);
                
                // 统计消息类型
                if (logEntry.message) {
                    messageTypes[logEntry.message] = (messageTypes[logEntry.message] || 0) + 1;
                }
                
                // 统计操作类型
                if (logEntry.messageType) {
                    operationTypes[logEntry.messageType] = (operationTypes[logEntry.messageType] || 0) + 1;
                }
            } catch (e) {
                // 忽略解析错误的行
            }
        });
        
        console.log('\n📊 日志类型统计:');
        Object.entries(messageTypes).forEach(([type, count]) => {
            console.log(`  ${type}: ${count} 条`);
        });
        
        console.log('\n🎯 操作类型统计:');
        Object.entries(operationTypes).forEach(([type, count]) => {
            console.log(`  ${type}: ${count} 次`);
        });
        
        // 显示最近的几条日志
        console.log('\n📝 最近的日志条目:');
        const recentLogs = lines.slice(-3);
        recentLogs.forEach((line, index) => {
            try {
                const logEntry = JSON.parse(line);
                console.log(`  ${index + 1}. [${logEntry.localtime}] ${logEntry.message}`);
                if (logEntry.messageType) {
                    console.log(`     操作: ${logEntry.messageType}`);
                }
                if (logEntry.stage) {
                    console.log(`     阶段: ${logEntry.stage}`);
                }
                if (logEntry.duration !== undefined) {
                    console.log(`     耗时: ${logEntry.duration}ms`);
                }
                console.log('');
            } catch (e) {
                console.log(`  ${index + 1}. [解析错误] ${line.substring(0, 100)}...`);
            }
        });
        
    } catch (error) {
        console.log(`❌ 读取日志文件失败: ${error.message}`);
    }
}

console.log('✅ 测试完成');
console.log('');
console.log('💡 提示:');
console.log('  - 如果没有日志文件，请在 KwaiPilot 中执行一些操作');
console.log('  - 日志文件以 chatId 为唯一标识，同一个聊天会复用同一个文件');
console.log('  - 可以使用 ./scripts/analyze-performance.sh 进行更详细的分析');

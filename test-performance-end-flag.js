#!/usr/bin/env node

/**
 * 测试性能日志结束标识功能
 * 验证 PERF_END_FLAG 是否能正确防止重复写入
 */

const fs = require('fs');
const path = require('path');
const os = require('os');

// 模拟日志路径
const LOG_PATH = path.join(os.homedir(), '.kwaipilot', 'logs');
const PERF_END_FLAG = 'chat-perf-end';

// 确保日志目录存在
if (!fs.existsSync(LOG_PATH)) {
  fs.mkdirSync(LOG_PATH, { recursive: true });
}

/**
 * 检查日志是否已结束
 */
function isLogEnded(chatId) {
  const logFilePath = path.join(LOG_PATH, `perf-agent-${chatId}.log`);
  if (!fs.existsSync(logFilePath)) {
    return false;
  }

  try {
    const logContent = fs.readFileSync(logFilePath, 'utf8');
    return logContent.includes(`"message":"${PERF_END_FLAG}"`);
  } catch (error) {
    console.warn(`Failed to check log end flag for chatId: ${chatId}`, error);
    return false;
  }
}

/**
 * 写入性能日志
 */
function writePerformanceLog(chatId, stage, duration) {
  if (isLogEnded(chatId)) {
    console.log(`❌ 日志已结束，跳过写入: chatId=${chatId}, stage=${stage}`);
    return false;
  }

  const logFilePath = path.join(LOG_PATH, `perf-agent-${chatId}.log`);
  const logEntry = {
    localtime: new Date().toLocaleString('zh-CN'),
    chatId,
    message: "performance",
    stage,
    duration,
    timestamp: Date.now()
  };

  fs.appendFileSync(logFilePath, JSON.stringify(logEntry) + '\n');
  console.log(`✅ 写入成功: chatId=${chatId}, stage=${stage}, duration=${duration}ms`);
  return true;
}

/**
 * 标记日志结束
 */
function markLogEnded(chatId) {
  if (isLogEnded(chatId)) {
    console.log(`⚠️  日志已经结束: chatId=${chatId}`);
    return;
  }

  const logFilePath = path.join(LOG_PATH, `perf-agent-${chatId}.log`);
  const endEntry = {
    localtime: new Date().toLocaleString('zh-CN'),
    chatId,
    message: PERF_END_FLAG,
    endTime: new Date().toISOString(),
    timestamp: Date.now()
  };

  fs.appendFileSync(logFilePath, JSON.stringify(endEntry) + '\n');
  console.log(`🏁 标记日志结束: chatId=${chatId}`);
}

/**
 * 运行测试
 */
function runTest() {
  const testChatId = `test-chat-${Date.now()}`;
  
  console.log('🧪 开始测试性能日志结束标识功能');
  console.log(`测试 chatId: ${testChatId}`);
  console.log('');

  // 1. 写入一些性能数据
  console.log('📝 步骤 1: 写入初始性能数据');
  writePerformanceLog(testChatId, 'init', 10);
  writePerformanceLog(testChatId, 'process', 50);
  writePerformanceLog(testChatId, 'complete', 20);
  console.log('');

  // 2. 检查日志状态
  console.log('🔍 步骤 2: 检查日志状态');
  console.log(`日志是否已结束: ${isLogEnded(testChatId) ? '是' : '否'}`);
  console.log('');

  // 3. 标记日志结束
  console.log('🏁 步骤 3: 标记日志结束');
  markLogEnded(testChatId);
  console.log('');

  // 4. 尝试继续写入数据（应该被阻止）
  console.log('🚫 步骤 4: 尝试继续写入数据（应该被阻止）');
  writePerformanceLog(testChatId, 'after_end_1', 30);
  writePerformanceLog(testChatId, 'after_end_2', 40);
  console.log('');

  // 5. 再次检查日志状态
  console.log('🔍 步骤 5: 再次检查日志状态');
  console.log(`日志是否已结束: ${isLogEnded(testChatId) ? '是' : '否'}`);
  console.log('');

  // 6. 显示最终日志内容
  console.log('📄 步骤 6: 显示最终日志内容');
  const logFilePath = path.join(LOG_PATH, `perf-agent-${testChatId}.log`);
  if (fs.existsSync(logFilePath)) {
    const logContent = fs.readFileSync(logFilePath, 'utf8');
    const lines = logContent.trim().split('\n');
    console.log(`日志文件: ${logFilePath}`);
    console.log(`总行数: ${lines.length}`);
    console.log('内容:');
    lines.forEach((line, index) => {
      try {
        const entry = JSON.parse(line);
        console.log(`  ${index + 1}. ${entry.message} - ${entry.stage || entry.endTime || 'N/A'}`);
      } catch (e) {
        console.log(`  ${index + 1}. [解析错误] ${line}`);
      }
    });
  }
  console.log('');

  console.log('✅ 测试完成！');
  console.log('');
  console.log('预期结果:');
  console.log('- 前3条记录应该成功写入');
  console.log('- 第4条记录是结束标识');
  console.log('- 后续的写入尝试应该被阻止');
  console.log('- 总共应该有4条记录');
}

// 运行测试
runTest();

#!/bin/bash

# 性能日志分析脚本
# 用于分析 KwaiPilot 的性能日志文件

LOG_DIR="$HOME/.kwaipilot/logs"

# 检查日志目录是否存在
if [ ! -d "$LOG_DIR" ]; then
    echo "日志目录不存在: $LOG_DIR"
    exit 1
fi

# 函数：显示帮助信息
show_help() {
    echo "KwaiPilot 性能日志分析工具"
    echo ""
    echo "用法: $0 [选项] [会话ID]"
    echo ""
    echo "选项:"
    echo "  -h, --help          显示此帮助信息"
    echo "  -l, --list          列出所有性能日志文件"
    echo "  -s, --summary       显示所有会话的性能摘要"
    echo "  -t, --top           显示最耗时的操作"
    echo "  -a, --analyze       分析指定聊天的详细性能数据"
    echo ""
    echo "示例:"
    echo "  $0 -l                           # 列出所有日志文件"
    echo "  $0 -s                           # 显示性能摘要"
    echo "  $0 -a chat-uuid-here            # 分析特定聊天"
    echo "  $0 -t                           # 显示最耗时的操作"
}

# 函数：列出所有性能日志文件
list_logs() {
    echo "性能日志文件列表:"
    echo "=================="
    find "$LOG_DIR" -name "perf-agent-*.log" -type f | while read -r file; do
        chat_id=$(basename "$file" | sed 's/perf-agent-\(.*\)\.log/\1/')
        file_size=$(du -h "$file" | cut -f1)
        mod_time=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || stat -c "%y" "$file" 2>/dev/null | cut -d' ' -f1-2)
        echo "聊天ID: $chat_id"
        echo "文件: $file"
        echo "大小: $file_size"
        echo "修改时间: $mod_time"
        echo "---"
    done
}

# 函数：显示性能摘要
show_summary() {
    echo "性能摘要统计:"
    echo "=============="
    
    # 统计总的操作次数
    total_operations=$(find "$LOG_DIR" -name "perf-vsc-*.log" -exec grep -h '"message":"summary"' {} \; | wc -l)
    echo "总操作次数: $total_operations"
    
    # 统计各种操作类型的次数
    echo ""
    echo "操作类型统计:"
    find "$LOG_DIR" -name "perf-agent-*.log" -exec grep -h '"message":"summary"' {} \; | \
        jq -r '.messageType' | sort | uniq -c | sort -nr

    # 平均耗时统计
    echo ""
    echo "平均耗时统计 (毫秒):"
    find "$LOG_DIR" -name "perf-agent-*.log" -exec grep -h '"message":"summary"' {} \; | \
        jq -r '"\(.messageType) \(.totalDuration)"' | \
        awk '{
            type[$1] += $2;
            count[$1]++
        } END {
            for (t in type)
                printf "%-15s: %.2f ms\n", t, type[t]/count[t]
        }' | sort -k2 -nr
}

# 函数：显示最耗时的操作
show_top_operations() {
    echo "最耗时的操作 (前10个):"
    echo "===================="
    find "$LOG_DIR" -name "perf-agent-*.log" -exec grep -h '"message":"summary"' {} \; | \
        jq -r '"\(.totalDuration) \(.messageType) \(.chatId) \(.localtime)"' | \
        sort -nr | head -10 | \
        awk '{printf "耗时: %6.0f ms | 类型: %-10s | 聊天: %.8s... | 时间: %s %s\n", $1, $2, $3, $4, $5}'

    echo ""
    echo "最耗时的单个阶段 (前10个):"
    echo "========================"
    find "$LOG_DIR" -name "perf-agent-*.log" -exec grep -h '"message":"performance"' {} \; | \
        jq -r '"\(.duration) \(.stage) \(.chatId) \(.localtime)"' | \
        sort -nr | head -10 | \
        awk '{printf "耗时: %6.0f ms | 阶段: %-25s | 聊天: %.8s... | 时间: %s %s\n", $1, $2, $3, $4, $5}'
}

# 函数：分析特定聊天
analyze_session() {
    local chat_id="$1"
    local log_file="$LOG_DIR/perf-agent-$chat_id.log"

    if [ ! -f "$log_file" ]; then
        echo "错误: 找不到聊天 $chat_id 的日志文件"
        echo "文件路径: $log_file"
        return 1
    fi

    echo "分析聊天: $chat_id"
    echo "======================"
    echo "日志文件: $log_file"
    echo ""
    
    # 显示会话的总体统计
    # 检查日志是否已结束
    local log_ended=$(grep -c '"message":"chat-perf-end"' "$log_file" 2>/dev/null || echo "0")
    if [ "$log_ended" -gt 0 ]; then
        echo "📋 日志状态: 已结束 ✅"
        local end_time=$(grep '"message":"chat-perf-end"' "$log_file" | tail -1 | jq -r '.localtime' 2>/dev/null || echo "未知")
        echo "结束时间: $end_time"
    else
        echo "📋 日志状态: 进行中 🔄"
    fi
    echo ""

    echo "总体统计:"
    grep '"message":"summary"' "$log_file" | \
        jq -r '"操作类型: \(.messageType) | 总耗时: \(.totalDuration)ms (\(.totalDurationSeconds)s) | 阶段数: \(.stagesCount) | 时间: \(.localtime)"'

    echo ""
    echo "各阶段详细耗时:"
    grep '"message":"performance"' "$log_file" | \
        jq -r '"阶段: \(.stage) | 耗时: \(.duration)ms (\(.durationSeconds)s)"' | \
        sort -k3 -nr

    echo ""
    echo "时间线:"
    grep '"message":"timestamp"' "$log_file" | \
        jq -r '"\(.localtime) | \(.stage)"' | \
        sort
}

# 主程序
case "$1" in
    -h|--help)
        show_help
        ;;
    -l|--list)
        list_logs
        ;;
    -s|--summary)
        show_summary
        ;;
    -t|--top)
        show_top_operations
        ;;
    -a|--analyze)
        if [ -z "$2" ]; then
            echo "错误: 请提供聊天ID"
            echo "用法: $0 -a <chat-id>"
            exit 1
        fi
        analyze_session "$2"
        ;;
    "")
        show_help
        ;;
    *)
        echo "错误: 未知选项 '$1'"
        echo "使用 '$0 --help' 查看帮助信息"
        exit 1
        ;;
esac

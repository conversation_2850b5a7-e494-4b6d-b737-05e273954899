# KwaiPilot 性能追踪功能

## 概述

为 KwaiPilot 的 ComposerService 添加了专门的性能追踪功能，可以自动记录各个异步操作阶段的耗时到独立的日志文件中。

## 功能特点

✅ **独立日志文件**: 每个聊天都有独立的性能日志文件 `perf-agent-${chatId}.log`
✅ **详细时间记录**: 记录每个异步操作的开始时间、结束时间和耗时
✅ **多种日志类型**: 支持性能统计、时间戳记录和总体统计
✅ **自动清理**: 会话切换和结束时自动清理日志记录器
✅ **错误处理**: 日志记录失败不影响主要功能
✅ **分析工具**: 提供脚本工具用于分析性能数据
✅ **结束标识**: 使用 `PERF_END_FLAG` 防止已结束的日志继续写入

## 实现的文件

### 核心实现
- `src/base/performance-logger/index.ts` - 性能日志记录器
- `src/services/composer/index.ts` - 集成性能追踪到 ComposerService

### 文档和工具
- `docs/performance-tracking.md` - 详细文档
- `scripts/analyze-performance.sh` - 性能分析脚本
- `README-performance.md` - 本文件

## 记录的操作阶段

### newTask 类型
- `init` - 初始化
- `getComposerSessionData` - 获取会话数据
- `getApiConversationHistory` - 获取API对话历史
- `$setEditingMessageTs` - 设置编辑消息时间戳
- `setCurrentMessageTs` - 设置当前消息时间戳
- `newTask-request` - 发送新任务请求

### stop 类型
- `init` - 初始化
- `cleanupUnSavedDeltaMessage` - 清理未保存的增量消息
- `cleanupInvalidWorkingSetEffects` - 清理无效的工作集效果
- `postComposerStateUpdate` - 发布状态更新
- `stop-request` - 发送停止请求

### restore 类型
- `init` - 初始化
- `restore-request` - 发送恢复请求

## 日志文件格式

日志文件位置: `~/.kwaipilot/logs/perf-agent-${chatId}.log`

每行都是一个 JSON 对象，包含以下类型：

### 1. 总体统计 (summary)
```json
{
  "localtime": "24/01/15 14:30:25.500",
  "chatId": "chat-uuid",
  "message": "summary",
  "messageType": "newTask",
  "totalDuration": 1250,
  "totalDurationSeconds": "1.250",
  "stagesCount": 6,
  "stages": [...]
}
```

### 2. 性能统计 (performance)
```json
{
  "localtime": "24/01/15 14:30:25.123",
  "chatId": "chat-uuid",
  "message": "performance",
  "stage": "newTask_getComposerSessionData",
  "duration": 45,
  "durationSeconds": "0.045",
  "messageType": "newTask",
  "stageIndex": 1,
  "timestamp": 1705304425123
}
```

### 3. 时间戳记录 (timestamp)
```json
{
  "localtime": "24/01/15 14:30:25.123",
  "chatId": "chat-uuid",
  "message": "timestamp",
  "stage": "newTask_getComposerSessionData",
  "timestamp": 1705304425123,
  "time": "2024-01-15T06:30:25.123Z",
  "messageType": "newTask",
  "stageIndex": 1,
  "isStart": false,
  "isEnd": false
}
```

## 使用分析工具

```bash
# 列出所有性能日志文件
./scripts/analyze-performance.sh -l

# 显示性能摘要
./scripts/analyze-performance.sh -s

# 显示最耗时的操作
./scripts/analyze-performance.sh -t

# 分析特定聊天
./scripts/analyze-performance.sh -a chat-uuid-here

# 显示帮助
./scripts/analyze-performance.sh -h
```

## 手动分析示例

```bash
# 查看某个聊天的所有性能记录
grep "chat-uuid" ~/.kwaipilot/logs/perf-agent-chat-uuid.log

# 查看所有 newTask 操作的总耗时
grep '"messageType":"newTask"' ~/.kwaipilot/logs/perf-agent-*.log | \
  grep '"message":"summary"' | jq '.totalDuration'

# 查看最耗时的操作阶段
grep '"message":"performance"' ~/.kwaipilot/logs/perf-agent-*.log | \
  jq -r '"\(.duration) \(.stage)"' | sort -nr | head -10

# 统计各种操作类型的平均耗时
grep '"message":"summary"' ~/.kwaipilot/logs/perf-agent-*.log | \
  jq -r '"\(.messageType) \(.totalDuration)"' | \
  awk '{type[$1] += $2; count[$1]++} END {for (t in type) printf "%s: %.2f ms\n", t, type[t]/count[t]}'
```

## 配置说明

- **文件大小限制**: 每个日志文件最大 10MB
- **文件数量限制**: 每个会话最多保留 5 个日志文件
- **时间格式**: 使用北京时间 (UTC+8)，精确到毫秒
- **自动清理**: 聊天切换时自动清理之前的日志记录器
- **结束标识**: 使用 `PERF_END_FLAG` 标记日志结束，防止重复写入
- **智能检查**: 自动检查日志文件是否已结束，避免无效操作

## 性能影响

- 日志记录是异步的，不会阻塞主要功能
- 记录失败时只会记录错误日志，不影响用户体验
- 内存占用很小，每个聊天只维护一个 Winston Logger 实例

## 扩展建议

1. **添加更多阶段**: 可以在其他异步操作中添加性能记录点
2. **实时监控**: 可以基于这些日志数据构建实时性能监控面板
3. **性能告警**: 当某个操作耗时超过阈值时发送告警
4. **数据可视化**: 将性能数据导入到图表工具中进行可视化分析

## 故障排除

如果性能日志没有生成：

1. 检查日志目录权限: `ls -la ~/.kwaipilot/logs/`
2. 检查磁盘空间: `df -h ~/.kwaipilot/`
3. 查看主日志中的错误信息
4. 确认 chatId 不为空

如果需要调试，可以在 ComposerService 中查看相关的错误日志。

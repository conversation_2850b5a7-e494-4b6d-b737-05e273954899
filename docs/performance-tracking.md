# Performance Tracking 性能追踪

## 概述

ComposerService 现在支持自动将各个阶段的耗时统计记录到专门的性能日志文件中，用于性能监控和分析。每个聊天都会有独立的性能日志文件：`perf-agent-${chatId}.log`。

### 核心特性

- **独立日志文件**: 每个 `chatId` 都有独立的性能日志文件
- **智能结束检查**: 使用 `PERF_END_FLAG` 标识防止已结束的日志继续写入
- **自动清理**: 聊天切换和结束时自动清理并标记日志结束
- **性能上报**: 支持将性能数据上报到 weblogger 系统

## 功能说明

在 `$postMessageToComposerEngine` 方法中，我们添加了 `performanceTimes` 数组来记录各个阶段的时间戳：

```typescript
const performanceTimes: {time: number, label: string; durtation?: number}[] = [
  {time: Date.now(), label: 'init'}
];
```

### 记录的阶段

根据消息类型，会记录以下阶段的耗时：

#### newTask 类型
- `init` - 初始化时间
- `getComposerSessionData` - 获取会话数据
- `getApiConversationHistory` - 获取API对话历史
- `$setEditingMessageTs` - 设置编辑消息时间戳
- `setCurrentMessageTs` - 设置当前消息时间戳
- `newTask-request` - 发送新任务请求

#### stop 类型
- `init` - 初始化时间
- `cleanupUnSavedDeltaMessage` - 清理未保存的增量消息
- `cleanupInvalidWorkingSetEffects` - 清理无效的工作集效果
- `postComposerStateUpdate` - 发布状态更新
- `stop-request` - 发送停止请求

#### restore 类型
- `init` - 初始化时间
- `restore-request` - 发送恢复请求

#### 其他类型
- `init` - 初始化时间
- `other-request` - 发送其他请求

## 性能日志文件

### 文件位置
性能日志文件保存在：`~/.kwaipilot/logs/perf-agent-${chatId}.log`

### 文件格式
每行都是一个 JSON 对象，包含以下字段：

```json
{
  "localtime": "24/01/15 14:30:25.123",
  "sessionId": "session-uuid-here",
  "message": "performance",
  "stage": "newTask_getComposerSessionData",
  "duration": 45,
  "durationSeconds": "0.045",
  "messageType": "newTask",
  "stageIndex": 1,
  "timestamp": 1705304425123
}
```

### 日志类型

#### 1. 性能统计 (performance)
记录每个阶段的具体耗时：
```json
{
  "localtime": "24/01/15 14:30:25.123",
  "sessionId": "session-uuid",
  "message": "performance",
  "stage": "newTask_getComposerSessionData",
  "duration": 45,
  "durationSeconds": "0.045",
  "messageType": "newTask",
  "stageIndex": 1,
  "timestamp": 1705304425123
}
```

#### 2. 时间戳记录 (timestamp)
记录每个阶段的时间点：
```json
{
  "localtime": "24/01/15 14:30:25.123",
  "sessionId": "session-uuid",
  "message": "timestamp",
  "stage": "newTask_getComposerSessionData",
  "timestamp": 1705304425123,
  "time": "2024-01-15T06:30:25.123Z",
  "messageType": "newTask",
  "stageIndex": 1,
  "isStart": false,
  "isEnd": false
}
```

#### 3. 总体统计 (summary)
记录整个操作的总体统计：
```json
{
  "localtime": "24/01/15 14:30:25.500",
  "sessionId": "session-uuid",
  "message": "summary",
  "messageType": "newTask",
  "totalDuration": 1250,
  "totalDurationSeconds": "1.250",
  "stagesCount": 6,
  "stages": [
    {"label": "init", "duration": undefined, "durationSeconds": undefined},
    {"label": "getComposerSessionData", "duration": 45, "durationSeconds": "0.045"},
    {"label": "getApiConversationHistory", "duration": 120, "durationSeconds": "0.120"},
    {"label": "$setEditingMessageTs", "duration": 15, "durationSeconds": "0.015"},
    {"label": "setCurrentMessageTs", "duration": 10, "durationSeconds": "0.010"},
    {"label": "newTask-request", "duration": 1060, "durationSeconds": "1.060"}
  ]
}
```

## 实现细节

### PerformanceLogger 类

位于 `src/base/performance-logger/index.ts`，提供以下主要方法：

- `logPerformance()` - 记录单个阶段的性能数据
- `logPerformanceTimestamp()` - 记录时间戳
- `logPerformanceSummary()` - 记录完整的性能统计
- `cleanup()` - 清理指定会话的日志记录器

### 自动清理

系统会在以下情况自动清理性能日志记录器：

1. **切换会话时** - 在 `$showTaskWithId` 方法中
2. **清理任务时** - 在 `clearTask` 方法中

### 错误处理

如果性能日志记录失败，会记录错误但不会影响主要功能：

```typescript
catch (error) {
  this.logger.error("Failed to log performance data", this.loggerScope, {
    err: error,
    value: {
      messageType: data.type,
      sessionId: this.sessionState.sessionId,
    },
  });
}
```

## 使用示例

当用户发送新任务时，系统会自动记录各个阶段的耗时。你可以在性能日志文件中看到类似这样的记录：

```
{"localtime":"24/01/15 14:30:25.123","sessionId":"abc-123","message":"summary","messageType":"newTask","totalDuration":1250,"totalDurationSeconds":"1.250","stagesCount":6,"stages":[...]}
{"localtime":"24/01/15 14:30:25.124","sessionId":"abc-123","message":"performance","stage":"newTask_getComposerSessionData","duration":45,"durationSeconds":"0.045","messageType":"newTask","stageIndex":1,"timestamp":1705304425123}
{"localtime":"24/01/15 14:30:25.125","sessionId":"abc-123","message":"timestamp","stage":"newTask_getComposerSessionData","timestamp":1705304425123,"time":"2024-01-15T06:30:25.123Z","messageType":"newTask","stageIndex":1,"isStart":false,"isEnd":false}
```

## 数据分析

通过性能日志文件收集的数据可以用于：

1. **性能监控** - 识别性能瓶颈
2. **用户体验优化** - 了解各阶段耗时分布
3. **系统调优** - 针对性优化慢速操作
4. **问题诊断** - 快速定位性能问题

### 分析脚本示例

你可以使用以下脚本来分析性能日志：

```bash
# 查看某个聊天的所有性能记录
grep "chat-uuid" ~/.kwaipilot/logs/perf-agent-chat-uuid.log

# 查看所有 newTask 操作的总耗时
grep '"messageType":"newTask"' ~/.kwaipilot/logs/perf-agent-*.log | grep '"message":"summary"' | jq '.totalDuration'

# 查看最耗时的操作阶段
grep '"message":"performance"' ~/.kwaipilot/logs/perf-agent-*.log | jq '.stage, .duration' | paste - -
```

## 配置说明

- **文件大小限制**: 每个日志文件最大 10MB
- **文件数量限制**: 每个会话最多保留 5 个日志文件
- **时间格式**: 使用北京时间 (UTC+8)
- **精度**: 毫秒级时间戳
